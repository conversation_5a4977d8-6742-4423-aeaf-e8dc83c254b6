{"permissions": {"allow": ["Bash(npx @angular/cli@latest new:*)", "Bash(npm install)", "Bash(npx ng generate component:*)", "Bash(npx ng build)", "Bash(npm run build:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(curl:*)", "Bash(npm start)", "<PERSON><PERSON>(pkill:*)", "Bash(npm install:*)", "Bash(npm run lint)", "Bash(npm remove:*)", "Bash(find:*)", "Bash(npx ng build:*)", "Bash(ls:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(pip uninstall:*)", "<PERSON><PERSON>(uv pip sync:*)", "<PERSON><PERSON>(python3:*)", "Bash(uv pip install:*)", "<PERSON><PERSON>(source:*)", "Bash(npx ng:*)", "Bash(grep:*)", "Bash(pip install:*)", "Bash(pip3 install:*)"], "deny": []}}