{"name": "agrimi-ai-chat", "version": "1.0.0", "description": "Angular 19 Chat Interface for Agrimi AI", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "dev": "ng serve --host 0.0.0.0 --port 4200", "test": "ng test", "serve": "ng serve --host 0.0.0.0 --port 4200"}, "private": true, "dependencies": {"@angular/animations": "^19.1.4", "@angular/common": "^19.1.4", "@angular/compiler": "^19.1.4", "@angular/core": "^19.1.4", "@angular/forms": "^19.1.4", "@angular/platform-browser": "^19.1.4", "@angular/platform-browser-dynamic": "^19.1.4", "@angular/router": "^19.1.4", "marked": "^16.1.1", "ng-zorro-antd": "^19.3.1", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.1.4", "@angular/cli": "^19.1.4", "@angular/compiler-cli": "^19.1.4", "@types/jasmine": "~5.1.0", "@types/node": "^18.7.0", "autoprefixer": "^10.4.0", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.4.0", "tailwindcss": "^3.4.0", "typescript": "~5.6.0"}}