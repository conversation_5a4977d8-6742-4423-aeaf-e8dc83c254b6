/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{html,ts}",
  ],
  theme: {
    extend: {
      colors: {
        'light-purple': '#AB85E8',
        'primary-teal': '#009C9C',
        'light-teal': '#7ACCCC',
        'neutral-light': '#F5F5F5',
        'neutral-lighter': '#D9D9D9',
        'chat-user-bg': '#E1F2F3',
        'suggestion-bg': '#F9F0FF',
        'text-primary': 'rgba(0, 0, 0, 0.85)',
        'text-secondary': 'rgba(0, 0, 0, 0.45)',
      },
      fontFamily: {
        'sans': ['Montserrat', '-apple-system', 'Roboto', 'Helvetica', 'sans-serif'],
      },
      borderRadius: {
        'message': '12px',
        'button': '8px',
        'input': '12px',
      },
      boxShadow: {
        'input': '0 2px 8px 0 rgba(0, 0, 0, 0.15)',
      }
    },
  },
  plugins: [],
}
