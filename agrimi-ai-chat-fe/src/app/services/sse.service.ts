import { Injectable, inject } from '@angular/core';
import { Observable, Subject, BehaviorSubject } from 'rxjs';
import { APP_CONFIG } from '../core/config/app-config.token';
import { ApiChatMessage, ChatRequest, SSEEvent } from '../models/api.models';


@Injectable({
  providedIn: 'root'
})
export class SSEService {
  private config = inject(APP_CONFIG);
  private eventSource: EventSource | null = null;
  private connectionState = new BehaviorSubject<'disconnected' | 'connecting' | 'connected'>('disconnected');
  
  public connectionState$ = this.connectionState.asObservable();

  private get baseUrl(): string {
    return this.config.apiUrl;
  }

  /**
   * Send a chat message and stream the response using standard EventSource
   */
  streamChat(request: ChatRequest): Observable<ApiChatMessage | { event: string; data: any }> {
    return new Observable(observer => {
      try {
        this.connectionState.next('connecting');
        
        // Build URL with query parameters for GET request
        const url = new URL(`${this.baseUrl}/chat/stream`);
        url.searchParams.append('query', request.query);
        url.searchParams.append('session_id', request.session_id);
        if (request.payload) {
          url.searchParams.append('payload', request.payload);
        }

        // Create standard EventSource
        this.eventSource = new EventSource(url.toString());
        
        // Handle connection open
        this.eventSource.onopen = () => {
          this.connectionState.next('connected');
        };

        // Handle standard message events (event: message)
        this.eventSource.addEventListener('message', (event: MessageEvent) => {
          try {
            const data = JSON.parse(event.data);
            observer.next(data as ApiChatMessage);
          } catch (e) {
            console.warn('Failed to parse message event data:', event.data);
          }
        });

        // Handle completion events (event: done)
        this.eventSource.addEventListener('done', (event: MessageEvent) => {
          try {
            const data = JSON.parse(event.data);
            if (data.status === 'completed') {
              observer.complete();
            }
          } catch (e) {
            console.warn('Failed to parse done event data:', event.data);
          }
        });

        // Handle error events (event: error)
        this.eventSource.addEventListener('error', (event: MessageEvent) => {
          try {
            const data = JSON.parse(event.data);
            observer.error(new Error(data.error || 'Server-sent error'));
          } catch (e) {
            // If we can't parse the error data, treat it as a connection error
            observer.error(new Error('SSE connection error'));
          }
        });

        // Handle connection errors
        this.eventSource.onerror = (error) => {
          this.connectionState.next('disconnected');
          observer.error(new Error('EventSource connection failed'));
        };

      } catch (error) {
        this.connectionState.next('disconnected');
        observer.error(error);
      }

      // Cleanup function
      return () => {
        this.disconnect();
      };
    });
  }

  /**
   * Close the current SSE connection
   */
  disconnect(): void {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
    this.connectionState.next('disconnected');
  }

  /**
   * Check if currently connected
   */
  isConnected(): boolean {
    return this.connectionState.value === 'connected';
  }
}