import { Injectable, signal, inject } from '@angular/core';
import { Observable, BehaviorSubject } from 'rxjs';
import { ChatSession } from '../models/chat.models';
import { SessionInfo } from '../models/api.models';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root'
})
export class SessionService {
  private apiService = inject(ApiService);
  
  // Current session state
  private currentSession = signal<ChatSession | null>(null);
  private sessionId = signal<string>('');
  
  // Public readonly signals
  public readonly currentSession$ = this.currentSession.asReadonly();
  public readonly sessionId$ = this.sessionId.asReadonly();

  constructor() {
    // Initialize with a new session ID
    this.initializeSession();
  }

  /**
   * Initialize a new session
   */
  private initializeSession(): void {
    const newSessionId = this.generateSessionId();
    this.sessionId.set(newSessionId);
    
    const newSession: ChatSession = {
      id: newSessionId,
      messages: [],
      isActive: true,
      createdAt: new Date(),
      lastActivity: new Date(),
      messageCount: 0
    };
    
    this.currentSession.set(newSession);
  }

  /**
   * Generate a unique session ID
   */
  private generateSessionId(): string {
    const timestamp = Date.now().toString(36);
    const randomPart = Math.random().toString(36).substr(2, 9);
    return `session_${timestamp}_${randomPart}`;
  }

  /**
   * Get current session ID
   */
  getCurrentSessionId(): string {
    return this.sessionId();
  }

  /**
   * Get current session
   */
  getCurrentSession(): ChatSession | null {
    return this.currentSession();
  }

  /**
   * Update session with new message count
   */
  updateSession(updates: Partial<ChatSession>): void {
    const current = this.currentSession();
    if (current) {
      const updated = {
        ...current,
        ...updates,
        lastActivity: new Date()
      };
      this.currentSession.set(updated);
    }
  }

  /**
   * Start a new session
   */
  startNewSession(): void {
    this.initializeSession();
  }

  /**
   * Get session info from the API
   */
  getSessionInfo(): Observable<SessionInfo> {
    const sessionId = this.getCurrentSessionId();
    return this.apiService.getSessionInfo(sessionId);
  }

  /**
   * Clear current session
   */
  clearCurrentSession(): Observable<{ status: string; session_id: string }> {
    const sessionId = this.getCurrentSessionId();
    return this.apiService.clearSession(sessionId);
  }

  /**
   * Sync session with backend
   */
  syncWithBackend(): void {
    this.getSessionInfo().subscribe({
      next: (info) => {
        this.updateSession({
          messageCount: info.message_count,
          lastActivity: info.last_activity ? new Date(info.last_activity) : new Date()
        });
      },
      error: (error) => {
        console.warn('Failed to sync session with backend:', error);
      }
    });
  }

  /**
   * Check if session is active
   */
  isSessionActive(): boolean {
    const session = this.currentSession();
    return session?.isActive || false;
  }
}