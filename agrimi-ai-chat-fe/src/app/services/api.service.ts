import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { APP_CONFIG } from '../core/config/app-config.token';
import { 
  ChatRequest, 
  ApiChatMessage, 
  SessionInfo, 
  HealthResponse, 
  ErrorResponse 
} from '../models/api.models';

@Injectable({
  providedIn: 'root'
})
export class ApiService {
  private http = inject(HttpClient);
  private config = inject(APP_CONFIG);
  
  private get baseUrl(): string {
    return this.config.apiUrl;
  }

  /**
   * Health check endpoint
   */
  healthCheck(): Observable<HealthResponse> {
    return this.http.get<HealthResponse>(`${this.baseUrl}/chat/health`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Send a quick reply payload
   */
  sendQuickReply(payload: string, sessionId: string): Observable<any> {
    return this.http.post(`${this.baseUrl}/chat/quick-reply`, {
      payload,
      session_id: sessionId
    }).pipe(catchError(this.handleError));
  }

  /**
   * Get session information
   */
  getSessionInfo(sessionId: string): Observable<SessionInfo> {
    return this.http.get<SessionInfo>(`${this.baseUrl}/chat/session/${sessionId}/info`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Clear session history
   */
  clearSession(sessionId: string): Observable<{ status: string; session_id: string }> {
    return this.http.delete<{ status: string; session_id: string }>(`${this.baseUrl}/chat/session/${sessionId}`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Test API connectivity
   */
  testConnection(): Observable<boolean> {
    return this.healthCheck().pipe(
      map(response => response.status === 'healthy'),
      catchError(() => throwError(() => new Error('API connection failed')))
    );
  }

  /**
   * Handle HTTP errors
   */
  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unknown error occurred';
    
    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Client Error: ${error.error.message}`;
    } else {
      // Server-side error
      if (error.error && error.error.error) {
        errorMessage = error.error.error;
      } else {
        errorMessage = `Server Error: ${error.status} - ${error.message}`;
      }
    }

    console.error('API Error:', error);
    return throwError(() => new Error(errorMessage));
  }
}