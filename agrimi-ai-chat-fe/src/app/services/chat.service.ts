import { Injectable, signal, inject, computed } from "@angular/core";
import { Observable, BehaviorSubject, EMPTY, of } from "rxjs";
import { catchError, tap, map } from "rxjs/operators";
import { Message, SuggestedQuestion, MessageState } from "../models/chat.models";
import { 
  ApiChatMessage, 
  ChatRequest, 
  ContentItem, 
  QuickReply as ApiQuickReply 
} from "../models/api.models";
import { SSEService } from "./sse.service";
import { SessionService } from "./session.service";
import { ApiService } from "./api.service";
import {parse as mdParse } from "marked";
@Injectable({
    providedIn: "root",
})
export class ChatService {
    private sseService = inject(SSEService);
    private sessionService = inject(SessionService);
    private apiService = inject(ApiService);

    // Message state
    private _messages = signal<Message[]>([]);
    private _isTyping = signal<boolean>(false);
    private _isConnected = signal<boolean>(false);
    private _error = signal<string | null>(null);
    private _currentQuickReplies = signal<ApiQuickReply[]>([]);

    // Public readonly signals
    public messages = this._messages.asReadonly();
    public isTyping = this._isTyping.asReadonly();
    public isConnected = this._isConnected.asReadonly();
    public error = this._error.asReadonly();
    public currentQuickReplies = this._currentQuickReplies.asReadonly();

    // Suggested questions - will be dynamically loaded from API or set as fallback
    public readonly suggestedQuestions: SuggestedQuestion[] = [
        {
            id: "1",
            text: "How can I help you today?",
            icon: "❓",
            payload: "help_general"
        },
        {
            id: "2", 
            text: "Tell me about your features",
            icon: "⚙️",
            payload: "features_info"
        },
        {
            id: "3",
            text: "How do I get started?",
            icon: "🚀",
            payload: "getting_started"
        }
    ];

    constructor() {
        // Initialize connection state monitoring
        this.sseService.connectionState$.subscribe((state: 'disconnected' | 'connecting' | 'connected') => {
            this._isConnected.set(state === 'connected');
        });

        // Test API connection on startup
        this.testApiConnection();
    }

    /**
     * Test API connection
     */
    private testApiConnection(): void {
        this.apiService.testConnection().subscribe({
            next: (connected) => {
                if (connected) {
                    this._error.set(null);
                    console.log('✅ API connection successful');
                }
            },
            error: (error) => {
                this._error.set('Failed to connect to API. Please check if the backend is running.');
                console.error('❌ API connection failed:', error);
            }
        });
    }

    /**
     * Send a message to the chat API
     */
    sendMessage(content: string, payload?: string): void {
        if (!content.trim() && !payload) return;

        const userMessage: Message = {
            id: this.generateId(),
            content: content.trim(),
            sender: "user",
            timestamp: new Date(),
        };

        // Add user message immediately
        const currentMessages = this._messages();
        this._messages.set([...currentMessages, userMessage]);

        // Clear any previous errors
        this._error.set(null);
        this._isTyping.set(true);

        // Prepare chat request
        const chatRequest: ChatRequest = {
            query: content.trim(),
            session_id: this.sessionService.getCurrentSessionId(),
            payload: payload
        };

        // Stream the response
        this.sseService.streamChat(chatRequest).subscribe({
            next: (response) => {
                this._isTyping.set(false);
                
                if (this.isApiChatMessage(response)) {
                    this.handleApiResponse(response);
                }
            },
            error: (error) => {
                this._isTyping.set(false);
                this._error.set(`Error: ${error.message}`);
                console.error('Chat error:', error);
                
                // Add error message for user feedback
                this.addErrorMessage("Sorry, I encountered an error. Please try again.");
            },
            complete: () => {
                this._isTyping.set(false);
                console.log('Chat stream completed');
            }
        });

        // Update session
        this.sessionService.updateSession({
            messageCount: (this.sessionService.getCurrentSession()?.messageCount || 0) + 1
        });
    }

    /**
     * Handle quick reply selection
     */
    handleQuickReply(quickReply: ApiQuickReply): void {
        this.sendMessage(quickReply.title, quickReply.payload);
        // Clear current quick replies after selection
        this._currentQuickReplies.set([]);
    }

    /**
     * Handle suggested question click
     */
    handleSuggestedQuestion(question: SuggestedQuestion): void {
        this.sendMessage(question.text, question.payload);
    }

    /**
     * Clear chat messages
     */
    clearChat(): void {
        this._messages.set([]);
        this._currentQuickReplies.set([]);
        this._error.set(null);
        
        // Also clear on backend
        this.sessionService.clearCurrentSession().subscribe({
            next: () => {
                console.log('Chat cleared successfully');
            },
            error: (error) => {
                console.warn('Failed to clear chat on backend:', error);
            }
        });

        // Start new session
        this.sessionService.startNewSession();
    }

    /**
     * Get current messages
     */
    getCurrentMessages(): Message[] {
        return this._messages();
    }

    /**
     * Check if response is ApiChatMessage
     */
    private isApiChatMessage(response: any): response is ApiChatMessage {
        return response && response.message && response.message.content;
    }

    /**
     * Handle API response and convert to internal Message format
     */
    private handleApiResponse(apiResponse: ApiChatMessage): void {
        const botMessage: Message = {
            id: this.generateId(),
            content: apiResponse.message.content,
            sender: "bot",
            timestamp: new Date(),
        };

        // Extract quick replies from the response
        const quickReplies = this.extractQuickReplies(apiResponse.message.content);
        if (quickReplies.length > 0) {
            botMessage.quickReplies = quickReplies;
            this._currentQuickReplies.set(quickReplies);
        }

        // Add bot message
        const currentMessages = this._messages();
        this._messages.set([...currentMessages, botMessage]);
    }

    /**
     * Extract quick replies from content items
     */
    private extractQuickReplies(content: ContentItem[]): ApiQuickReply[] {
        for (const item of content) {
            if (item.type === 'quick_replies') {
                return item.replies;
            }
        }
        return [];
    }

    /**
     * Add an error message
     */
    private addErrorMessage(errorText: string): void {
        const errorMessage: Message = {
            id: this.generateId(),
            content: errorText,
            sender: "bot",
            timestamp: new Date(),
            metadata: { isError: true }
        };

        const currentMessages = this._messages();
        this._messages.set([...currentMessages, errorMessage]);
    }

    /**
     * Generate unique ID
     */
    private generateId(): string {
        return Math.random().toString(36).substr(2, 9);
    }

    /**
     * Get connection status
     */
    getConnectionStatus(): 'connected' | 'disconnected' | 'error' {
        if (this._error()) return 'error';
        return this._isConnected() ? 'connected' : 'disconnected';
    }

    /**
     * Retry connection
     */
    retryConnection(): void {
        this._error.set(null);
        this.testApiConnection();
    }
}