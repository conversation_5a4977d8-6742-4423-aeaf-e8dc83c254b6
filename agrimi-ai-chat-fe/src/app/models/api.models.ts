// Interfaces matching the FastAPI backend response models

export interface QuickReply {
  title: string;
  payload: string;
}

export interface TextContent {
  type: 'text';
  text: string;
}

export interface QuickRepliesContent {
  type: 'quick_replies';
  replies: QuickReply[];
}

export interface TableContent {
  type: 'table';
  headers: string[];
  rows: string[][];
  caption?: string;
}

export interface ListContent {
  type: 'list';
  items: { [key: string]: any }[];
  title?: string;
}

export type ContentItem = TextContent | QuickRepliesContent | TableContent | ListContent;

export interface ApiMessage {
  role: 'user' | 'assistant';
  content: ContentItem[];
}

export interface ApiChatMessage {
  message: ApiMessage;
}

export interface ChatRequest {
  query: string;
  session_id: string;
  payload?: string;
}

export interface SessionInfo {
  session_id: string;
  message_count: number;
  created_at?: string;
  last_activity?: string;
}

export interface ErrorResponse {
  error: string;
  detail?: string;
}

export interface SSEEvent {
  event: 'message' | 'done' | 'error';
  data: string;
}

export interface HealthResponse {
  status: string;
  service?: string;
  version?: string;
}