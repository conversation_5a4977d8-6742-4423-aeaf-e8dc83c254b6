import { ContentItem, QuickReply as ApiQuickReply } from './api.models';

export interface Message {
  id: string;
  content: string | ContentItem[]; // Support both text and structured content
  sender: 'user' | 'bot';
  timestamp: Date;
  metadata?: any;
  quickReplies?: ApiQuickReply[]; // Add support for quick replies
}

export interface MessageState {
  loading: boolean;
  error?: string;
}

export interface ChatSession {
  id: string;
  messages: Message[];
  isActive: boolean;
  messageCount?: number;
  createdAt?: Date;
  lastActivity?: Date;
}

export interface CollapsibleContent {
  title: string;
  isExpanded: boolean;
  links?: LinkItem[];
}

export interface LinkItem {
  text: string;
  url?: string;
  icon?: string;
}

export interface SuggestedQuestion {
  id: string;
  text: string;
  icon?: string;
  payload?: string; // Add payload support for API integration
}
