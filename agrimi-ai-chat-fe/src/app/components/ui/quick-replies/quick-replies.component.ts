import { Component, input, output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { QuickReply } from '../../../models/api.models';

@Component({
  selector: 'app-quick-replies',
  standalone: true,
  imports: [CommonModule, NzButtonModule, NzIconModule],
  template: `
    <div class="quick-replies-container" *ngIf="replies().length > 0">
      <div class="quick-replies-header">
        <span class="quick-replies-title">Quick replies:</span>
      </div>
      <div class="quick-replies-grid">
        <button
          *ngFor="let reply of replies(); trackBy: trackByReply"
          nz-button
          nzType="default"
          nzSize="small"
          class="quick-reply-button"
          (click)="onReplyClick(reply)"
          [attr.aria-label]="'Quick reply: ' + reply.title"
        >
          <span class="quick-reply-text">{{ reply.title }}</span>
        </button>
      </div>
    </div>
  `,
  styles: [`
    .quick-replies-container {
      margin-top: 0.75rem;
      padding: 0.75rem;
      background-color: #f8f9fa;
      border-radius: 0.5rem;
      border: 1px solid #e9ecef;
    }

    .quick-replies-header {
      margin-bottom: 0.5rem;
    }

    .quick-replies-title {
      font-size: 0.875rem;
      font-weight: 500;
      color: #6c757d;
    }

    .quick-replies-grid {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
    }

    .quick-reply-button {
      background-color: white;
      border: 1px solid #d1d5db;
      transition: all 0.2s ease-in-out;
      max-width: 100%;
    }

    .quick-reply-button:hover {
      background-color: #f3f4f6;
      border-color: #9ca3af;
      transform: translateY(-1px);
    }

    .quick-reply-button:active {
      transform: translateY(0);
    }

    .quick-reply-text {
      display: block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 200px;
    }

    @media (max-width: 640px) {
      .quick-replies-grid {
        flex-direction: column;
      }
      
      .quick-reply-button {
        width: 100%;
      }
      
      .quick-reply-text {
        max-width: none;
      }
    }
  `]
})
export class QuickRepliesComponent {
  replies = input.required<QuickReply[]>();
  replyClick = output<QuickReply>();

  onReplyClick(reply: QuickReply): void {
    this.replyClick.emit(reply);
  }

  trackByReply(index: number, reply: QuickReply): string {
    return reply.payload || reply.title;
  }
}