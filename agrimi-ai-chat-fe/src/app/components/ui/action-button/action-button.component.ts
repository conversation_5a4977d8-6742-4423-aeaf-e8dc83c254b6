import { Component, input, output, computed } from "@angular/core";
import { CommonModule } from "@angular/common";

@Component({
    selector: "app-action-button",
    imports: [CommonModule],
    templateUrl: "./action-button.component.html",
    styles: [
        `
            button:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }
        `,
    ],
})
export class ActionButtonComponent {
    text = input<string>("");
    variant = input<"primary" | "secondary" | "outline">("secondary");
    disabled = input<boolean>(false);
    click = output<void>();

    buttonClasses = computed(() => {
        const baseClasses =
            "flex items-center gap-2 px-3 py-1 rounded-button transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2";

        const variantClasses = {
            primary:
                "bg-light-purple text-white hover:bg-opacity-90 focus:ring-light-purple",
            secondary:
                "bg-neutral-light border border-neutral-lighter text-text-primary hover:bg-neutral-lighter focus:ring-neutral-lighter",
            outline:
                "bg-white border border-neutral-lighter text-text-primary hover:border-light-purple focus:ring-light-purple",
        };

        return `${baseClasses} ${variantClasses[this.variant()]}`;
    });

    onClick(): void {
        if (!this.disabled()) {
            this.click.emit();
        }
    }
}
