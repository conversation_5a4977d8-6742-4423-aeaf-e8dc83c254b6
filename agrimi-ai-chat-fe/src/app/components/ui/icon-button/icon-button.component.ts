import { Component, input, output, computed } from "@angular/core";
import { CommonModule } from "@angular/common";

@Component({
    selector: "app-icon-button",
    imports: [CommonModule],
    templateUrl: "./icon-button.component.html",
    styles: [
        `
            button:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }

            button:not(:disabled):hover {
                background-color: rgba(0, 0, 0, 0.05);
            }
        `,
    ],
})
export class IconButtonComponent {
    variant = input<"primary" | "secondary" | "ghost">("secondary");
    size = input<"small" | "medium" | "large">("medium");
    disabled = input<boolean>(false);
    ariaLabel = input<string>("");
    click = output<void>();

    buttonClasses = computed(() => {
        const baseClasses =
            "icon-button transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2";

        const sizeClasses = {
            small: "w-6 h-6 p-0.5",
            medium: "w-8 h-8 p-1",
            large: "w-10 h-10 p-2",
        };

        const variantClasses = {
            primary:
                "bg-light-purple text-white hover:bg-opacity-90 focus:ring-light-purple",
            secondary:
                "bg-white text-text-primary border border-neutral-lighter hover:bg-neutral-light focus:ring-neutral-lighter",
            ghost: "bg-transparent text-text-secondary hover:bg-neutral-light focus:ring-neutral-lighter",
        };

        return `${baseClasses} ${sizeClasses[this.size()]} ${
            variantClasses[this.variant()]
        }`;
    });

    onClick(): void {
        if (!this.disabled()) {
            this.click.emit();
        }
    }
}
