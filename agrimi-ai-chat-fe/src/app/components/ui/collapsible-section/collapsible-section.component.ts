import { Component, input, output } from "@angular/core";
import { CommonModule } from "@angular/common";
import {
    trigger,
    state,
    style,
    transition,
    animate,
} from "@angular/animations";
import { LinkButtonComponent } from "../link-button/link-button.component";
import { NzIconDirective, NzIconModule } from "ng-zorro-antd/icon";

@Component({
    selector: "app-collapsible-section",
    imports: [CommonModule, LinkButtonComponent, NzIconModule],
    templateUrl: "./collapsible-section.component.html",
    animations: [
        trigger("expandCollapse", [
            state(
                "collapsed",
                style({
                    height: "0px",
                    overflow: "hidden",
                    paddingTop: "0",
                    paddingBottom: "0",
                })
            ),
            state(
                "expanded",
                style({
                    height: "*",
                    overflow: "visible",
                    paddingTop: "4px",
                    paddingBottom: "0",
                })
            ),
            transition("collapsed => expanded", animate("200ms ease-out")),
            transition("expanded => collapsed", animate("200ms ease-in")),
        ]),
    ],
})
export class CollapsibleSectionComponent {
    title = input<string>("");
    links = input<any[]>([]);
    isExpanded = input<boolean>(false);
    toggle = output<void>();
    linkClick = output<any>();

    onToggleClick(): void {
        this.toggle.emit();
    }

    onLinkClick(link: any): void {
        this.linkClick.emit(link);
    }
}
