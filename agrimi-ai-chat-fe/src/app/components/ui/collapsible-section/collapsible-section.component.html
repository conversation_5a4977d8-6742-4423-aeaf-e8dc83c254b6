<div class="flex flex-col gap-2">
    <button
        (click)="onToggleClick()"
        class="flex items-center gap-2 p-1 text-primary-teal hover:text-opacity-80 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-teal focus:ring-offset-2 rounded"
        type="button"
    >
        <div
            class="w-3.5 h-3.5 transition-transform duration-200"
            [class.rotate-180]="isExpanded()"
        >
            <svg
                viewBox="0 0 14 14"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                class="w-full h-full"
            >
                <path
                    d="M11.62 5.22083L7.81671 9.02416C7.36754 9.47333 6.63255 9.47333 6.18338 9.02416L2.38004 5.22083"
                    stroke="currentColor"
                    stroke-width="1.5"
                    stroke-miterlimit="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                />
            </svg>
        </div>
        <span class="text-sm font-medium">{{ title() }}</span>
    </button>

    <div
        [@expandCollapse]="isExpanded() ? 'expanded' : 'collapsed'"
        class="overflow-hidden"
    >
        <div class="flex flex-col gap-2 pt-1">
            @for (link of links(); track link.text) {
            <app-link-button [text]="link.text" (click)="onLinkClick(link)">
                <div slot="icon" class="w-4 h-4 text-primary-teal">
                    <nz-icon
                        nzType="file-text"
                        nzTheme="outline"
                        class="text-wite"
                    />
                </div>
            </app-link-button>
            }
        </div>
    </div>
</div>
