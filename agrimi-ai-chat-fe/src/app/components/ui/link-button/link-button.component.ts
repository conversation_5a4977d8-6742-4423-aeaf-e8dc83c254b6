import { Component, input, output } from "@angular/core";
import { CommonModule } from "@angular/common";

@Component({
    selector: "app-link-button",
    imports: [CommonModule],
    templateUrl: "./link-button.component.html",
    styles: [
        `
            button:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }
        `,
    ],
})
export class LinkButtonComponent {
    text = input<string>("");
    disabled = input<boolean>(false);
    click = output<void>();

    onClick(): void {
        if (!this.disabled()) {
            this.click.emit();
        }
    }
}
