import { Component, OnInit, computed, effect, inject } from "@angular/core";
import { CommonModule } from "@angular/common";
import { AppHeaderComponent } from "../app-header/app-header.component";
import { ChatNavigationComponent } from "../chat-navigation/chat-navigation.component";
import { ChatListComponent } from "../chat-list/chat-list.component";
import { ChatInputComponent } from "../../interactive/chat-input/chat-input.component";
import { SuggestedQuestionsComponent } from "../../interactive/suggested-questions/suggested-questions.component";
import { ChatService } from "../../../services/chat.service";
import { SessionService } from "../../../services/session.service";
import { Message, SuggestedQuestion } from "../../../models/chat.models";
import { QuickReply } from "../../../models/api.models";

@Component({
    selector: "app-chat-container",
    imports: [
        CommonModule,
        AppHeaderComponent,
        Chat<PERSON><PERSON>gationComponent,
        ChatL<PERSON>Component,
        ChatInputComponent,
        SuggestedQuestionsComponent,
    ],
    templateUrl: "./chat-container.component.html",
})
export class ChatContainerComponent implements OnInit {
    private sessionService = inject(SessionService);

    messages = computed(() => this.chatService.messages());
    isTyping = computed(() => this.chatService.isTyping());

    // Session management
    connectionStatus = computed(() => this.chatService.getConnectionStatus());
    sessionId = computed(() => this.sessionService.sessionId$());
    currentSession = computed(() => this.sessionService.currentSession$());
    isConnected = computed(() => this.chatService.isConnected());
    error = computed(() => this.chatService.error());

    // Combine fallback suggested questions with dynamic quick replies from API
    suggestedQuestions = computed(() => {
        const currentQuickReplies = this.chatService.currentQuickReplies();
        const hasMessages = this.messages().length > 0;

        // If we have quick replies from the API, convert them to suggested questions
        if (currentQuickReplies.length > 0) {
            return currentQuickReplies.map(
                (reply, index) =>
                    ({
                        id: `quick_reply_${index}`,
                        text: reply.title,
                        icon: "💬",
                        payload: reply.payload,
                    } as SuggestedQuestion)
            );
        }

        // If no messages yet, show default suggested questions
        if (!hasMessages) {
            return this.chatService.suggestedQuestions;
        }

        // If there are messages but no quick replies, don't show suggestions
        return [];
    });

    constructor(private chatService: ChatService) {
        // Monitor session state changes
        effect(() => {
            const session = this.currentSession();
            const status = this.connectionStatus();

            if (session) {
                console.log(`Session initialized: ${session.id}`);
                console.log(`Connection status: ${status}`);
            }
        });
    }

    ngOnInit(): void {
        // Initialize session management
        this.initializeSessionManagement();
    }

    onSendMessage(message: string): void {
        this.chatService.sendMessage(message);
    }

    onSuggestedQuestionClick(question: SuggestedQuestion): void {
        this.chatService.handleSuggestedQuestion(question);
    }

    onQuickReplyClick(quickReply: QuickReply): void {
        this.chatService.handleQuickReply(quickReply);
    }

    onNewChat(): void {
        this.chatService.clearChat();
        console.log("New chat started");
    }

    onChatHistory(): void {
        console.log("Chat history requested");
    }

    onHelp(): void {
        console.log("Help requested");
    }

    onPhone(): void {
        console.log("Phone support requested");
    }

    onExpand(): void {
        console.log("Chat expanded");
    }

    onClose(): void {
        console.log("Chat closed");
    }

    // Session Management Methods
    private initializeSessionManagement(): void {
        // Sync session with backend on initialization
        this.syncSession();
    }

    syncSession(): void {
        this.sessionService.syncWithBackend();
        console.log("Session sync requested");
    }

    retryConnection(): void {
        this.chatService.retryConnection();
        console.log("Connection retry requested");
    }

    getSessionStatus(): string {
        const session = this.currentSession();
        const status = this.connectionStatus();

        if (!session) return "No session";
        if (status === "error") return "Connection error";
        if (!this.isConnected()) return "Disconnected";

        return `Active (${session.messageCount || 0} messages)`;
    }

    shouldShowSessionError(): boolean {
        return this.connectionStatus() === "error" || !!this.error();
    }
}
