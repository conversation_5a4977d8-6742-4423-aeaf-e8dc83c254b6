<div class="chat-container">
    <!-- Header -->
    <app-header (expand)="onExpand()" (close)="onClose()"> </app-header>

    <!-- Navigation -->
    <app-chat-navigation
        (newChat)="onNewChat()"
        (chatHistory)="onChatHistory()"
        (help)="onHelp()"
        (phone)="onPhone()"
    >
    </app-chat-navigation>

    <!-- Chat Messages -->
    <app-chat-list
        [messages]="messages()"
        [isTyping]="isTyping()"
        (quickReplyClick)="onQuickReplyClick($event)"
        class="overflow-hidden flex-1"
    >
    </app-chat-list>

    <!-- Suggested Questions -->
    <app-suggested-questions
        [questions]="suggestedQuestions()"
        (questionClick)="onSuggestedQuestionClick($event)"
    >
    </app-suggested-questions>

    <!-- Chat Input -->
    <app-chat-input (sendMessage)="onSendMessage($event)"> </app-chat-input>
</div>
