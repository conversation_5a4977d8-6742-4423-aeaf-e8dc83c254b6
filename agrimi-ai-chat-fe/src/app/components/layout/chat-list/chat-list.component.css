.message-container {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.typing-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #ab85e8;
    animation: typing 1.4s infinite ease-in-out;
}

@keyframes typing {
    0%,
    60%,
    100% {
        transform: translateY(0);
        opacity: 0.4;
    }
    30% {
        transform: translateY(-10px);
        opacity: 1;
    }
}

/* Custom scrollbar styling */
.chat-scroll-container {
    scrollbar-width: thin;
    scrollbar-color: #d1d5db #f9fafb;
}

.chat-scroll-container::-webkit-scrollbar {
    width: 8px;
}

.chat-scroll-container::-webkit-scrollbar-track {
    background: #f9fafb;
    border-radius: 4px;
}

.chat-scroll-container::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 4px;
}

.chat-scroll-container::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

.chat-scroll-container:focus {
    outline: 2px solid #009c9c;
    outline-offset: -2px;
}
