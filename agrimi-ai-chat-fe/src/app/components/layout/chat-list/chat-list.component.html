<div
    #scrollContainer
    class="overflow-y-auto h-full chat-scroll-container px-3 sm:px-6 py-3 sm:py-4"
    tabindex="0"
    role="log"
    aria-label="Chat messages"
>
    @for (message of messages(); track trackByMessageId($index, message)) {
    <div class="message-container">
        @if (message.sender === 'user') {
        <app-user-message [message]="message"></app-user-message>
        } @if (message.sender === 'bot') {
        <app-bot-message [message]="message" (quickReplyClick)="onQuickReplyClick($event)"></app-bot-message>
        }
    </div>
    }

    <!-- Typing indicator -->
    @if (isTyping()) {
    <div class="flex justify-start pr-10 mb-6">
        <div class="bg-white rounded-message p-4">
            <div class="flex items-center gap-1">
                <div class="typing-dot"></div>
                <div class="typing-dot" style="animation-delay: 0.2s"></div>
                <div class="typing-dot" style="animation-delay: 0.4s"></div>
            </div>
        </div>
    </div>
    }
</div>
