import { CommonModule } from "@angular/common";
import {
    AfterViewChecked,
    AfterViewInit,
    Component,
    effect,
    ElementRef,
    input,
    OnDestroy,
    output,
    signal,
    ViewChild,
} from "@angular/core";
import { Message } from "../../../models/chat.models";
import { QuickReply } from "../../../models/api.models";
import { BotMessageComponent } from "../../message/bot-message/bot-message.component";
import { UserMessageComponent } from "../../message/user-message/user-message.component";

@Component({
    selector: "app-chat-list",
    imports: [CommonModule, BotMessageComponent, UserMessageComponent],
    templateUrl: "./chat-list.component.html",
    styleUrls: ["./chat-list.component.css"],
})
export class ChatListComponent
    implements AfterViewChecked, AfterViewInit, OnD<PERSON>roy
{
    @ViewChild("scrollContainer") scrollContainer!: ElementRef;
    messages = input<Message[]>([]);
    isTyping = input<boolean>(false);
    quickReplyClick = output<QuickReply>();

    private shouldScrollToBottom = false;
    private isUserScrolling = signal(false);
    private lastMessageCount = 0;
    private scrollTimeout: any;

    constructor() {
        // Use effect to react to signal changes
        effect(() => {
            const currentMessages = this.messages();
            const currentTyping = this.isTyping();

            // Check if messages have changed (new message added)
            const messageCountChanged =
                currentMessages.length !== this.lastMessageCount;

            if (messageCountChanged || currentTyping) {
                this.lastMessageCount = currentMessages.length;
                this.shouldScrollToBottom = true;

                // Reset user scrolling flag when new content arrives
                if (messageCountChanged) {
                    this.isUserScrolling.set(false);
                    // console.log("📨 New message detected, triggering auto-scroll");
                }

                if (currentTyping) {
                    // console.log("⌨️ Typing indicator changed, triggering auto-scroll");
                }
            }
        });
    }

    ngAfterViewInit(): void {
        // Set up scroll event listener to detect user scrolling
        this.setupScrollListener();

        // Initial scroll to bottom on component initialization
        setTimeout(() => {
            // console.log("🚀 Initial scroll to bottom on component load");
            this.scrollToBottom();
        }, 0);
    }

    ngAfterViewChecked(): void {
        if (this.shouldScrollToBottom && !this.isUserScrolling()) {
            // Use setTimeout to ensure DOM is fully updated
            setTimeout(() => {
                // console.log("⬇️ Auto-scrolling to bottom after view update");
                this.scrollToBottom();
                this.shouldScrollToBottom = false;
            }, 0);
        } else if (this.shouldScrollToBottom && this.isUserScrolling()) {
            // console.log("🚫 Auto-scroll blocked - user is manually scrolling");
            this.shouldScrollToBottom = false;
        }
    }

    ngOnDestroy(): void {
        // Clean up timeout to prevent memory leaks
        if (this.scrollTimeout) {
            clearTimeout(this.scrollTimeout);
        }
    }

    trackByMessageId(index: number, message: Message): string {
        return message.id;
    }

    onQuickReplyClick(quickReply: QuickReply): void {
        this.quickReplyClick.emit(quickReply);
    }

    private setupScrollListener(): void {
        if (!this.scrollContainer?.nativeElement) return;

        const element = this.scrollContainer.nativeElement;

        element.addEventListener("scroll", () => {
            // Clear any existing timeout
            if (this.scrollTimeout) {
                clearTimeout(this.scrollTimeout);
            }

            // Check if user is scrolling up (not at bottom)
            const isAtBottom = this.isScrolledToBottom();

            if (!isAtBottom) {
                this.isUserScrolling.set(true);

                // Reset user scrolling flag after a delay if they stop scrolling
                this.scrollTimeout = setTimeout(() => {
                    // Only reset if still not at bottom
                    if (!this.isScrolledToBottom()) {
                        this.isUserScrolling.set(false);
                    }
                }, 2000); // 2 seconds delay
            } else {
                this.isUserScrolling.set(false);
            }
        });
    }

    private isScrolledToBottom(): boolean {
        if (!this.scrollContainer?.nativeElement) return false;

        const element = this.scrollContainer.nativeElement;
        const threshold = 50; // Allow 50px tolerance

        return (
            element.scrollTop + element.clientHeight >=
            element.scrollHeight - threshold
        );
    }

    private scrollToBottom(): void {
        try {
            if (!this.scrollContainer?.nativeElement) return;

            const element = this.scrollContainer.nativeElement;

            // Smooth scroll to bottom
            element.scrollTo({
                top: element.scrollHeight,
                behavior: "smooth",
            });

            // Fallback for browsers that don't support smooth scrolling
            if (
                element.scrollTop !==
                element.scrollHeight - element.clientHeight
            ) {
                element.scrollTop = element.scrollHeight;
            }
        } catch (err) {
            console.error("Could not scroll to bottom:", err);
        }
    }
}
