<div class="flex justify-between items-center p-4 bg-light-purple">
    <div class="flex items-center gap-2">
        <div class="w-8 h-8 flex items-center justify-center rounded-lg">
            <nz-icon
                nzType="ai-icon:app"
                nzTheme="outline"
                class="text-white text-2xl"
            />
            <!-- <svg
                class="w-7 h-7 text-white"
                viewBox="0 0 28 27"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
            >
                <path
                    fill="currentColor"
                    d="M23.572 20.13a.275.275 0 0 1 .523 0l.678 2.069c.082.25.277.445.527.527l2.07.68a.275.275 0 0 1 0 .521l-2.07.68a.83.83 0 0 0-.527.527l-.678 2.07a.275.275 0 0 1-.523 0l-.68-2.07a.83.83 0 0 0-.526-.527l-2.07-.68a.275.275 0 0 1 0-.522l2.07-.679a.83.83 0 0 0 .526-.527zM13.661 1.782a1.03 1.03 0 0 1 1.01 0c.364.206.599 1.017 1.07 2.64l1.31 4.512c.18.617.27.926.437 1.18.149.223.341.415.566.563.253.168.561.258 1.178.437l4.513 1.31c1.622.472 2.433.708 2.639 1.07.177.314.177.697 0 1.01-.206.364-1.017.6-2.64 1.071l-4.512 1.31c-.617.18-.925.27-1.178.437a2 2 0 0 0-.566.566c-.167.253-.257.562-.436 1.178l-1.311 4.512c-.471 1.622-.707 2.434-1.07 2.64a1.03 1.03 0 0 1-1.01 0c-.363-.206-.598-1.018-1.07-2.64l-1.31-4.512c-.179-.616-.269-.925-.437-1.178a2 2 0 0 0-.565-.566c-.253-.168-.562-.257-1.179-.436l-4.512-1.311c-1.623-.471-2.433-.707-2.639-1.07a1.02 1.02 0 0 1 0-1.01c.206-.363 1.016-.6 2.639-1.07l4.512-1.31c.617-.18.926-.27 1.18-.437.224-.149.416-.34.565-.565.167-.253.257-.562.436-1.179l1.31-4.512c.472-1.623.707-2.434 1.07-2.64M4.238.797a.275.275 0 0 1 .523 0l.68 2.069c.081.25.276.445.526.526l2.07.68a.275.275 0 0 1 0 .523l-2.07.68a.83.83 0 0 0-.527.526L4.76 7.87a.275.275 0 0 1-.522 0L3.56 5.8a.83.83 0 0 0-.527-.526l-2.07-.68a.275.275 0 0 1 0-.522l2.07-.68a.83.83 0 0 0 .527-.526z"
                />
            </svg> -->
        </div>
        <h1 class="text-white text-base font-bold">Agrimi AI</h1>
    </div>

    <div class="flex items-center gap-3">
        <app-icon-button
            variant="ghost"
            (click)="onExpand()"
            ariaLabel="Expand chat"
        >
            <nz-icon
                nzType="expand-alt"
                nzTheme="outline"
                class="text-lg text-white"
            />
        </app-icon-button>

        <app-icon-button
            variant="ghost"
            (click)="onClose()"
            ariaLabel="Close chat"
        >
            <nz-icon
                nzType="close"
                nzTheme="outline"
                class="text-lg text-white"
            />
        </app-icon-button>
    </div>
</div>
