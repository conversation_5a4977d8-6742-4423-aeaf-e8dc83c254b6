import { Component, output } from "@angular/core";
import { CommonModule } from "@angular/common";
import { IconButtonComponent } from "../../ui/icon-button/icon-button.component";
import { NzIconModule } from "ng-zorro-antd/icon";

@Component({
    selector: "app-header",
    imports: [CommonModule, IconButtonComponent, NzIconModule],
    templateUrl: "./app-header.component.html",
})
export class AppHeaderComponent {
    expand = output<void>();
    close = output<void>();

    onExpand(): void {
        this.expand.emit();
    }

    onClose(): void {
        this.close.emit();
    }
}
