<div
    class="flex justify-between items-center p-3 sm:p-4 bg-white border-b border-neutral-lighter"
>
    <div class="flex items-center gap-3">
        <button
            nz-button
            (click)="onNewChat()"
            class="new-chat-button"
            type="button"
            aria-label="Start new conversation"
        >
            <span class="button-icon">
                <nz-icon nzType="plus" nzTheme="outline" />
            </span>
            <span class="button-text">Нов разговор</span>
        </button>

        <app-icon-button
            variant="secondary"
            (click)="onChatHistory()"
            ariaLabel="Chat history"
        >
            <nz-icon
                nzType="message"
                nzTheme="outline"
                class="text-xl text-text-secondary"
            />
        </app-icon-button>
    </div>

    <div class="flex items-center gap-3">
        <app-icon-button variant="ghost" (click)="onHelp()" ariaLabel="Help">
            <nz-icon
                nzType="question-circle"
                nzTheme="outline"
                class="text-2xl text-text-secondary"
            />
        </app-icon-button>

        <app-icon-button variant="ghost" (click)="onPhone()" ariaLabel="Phone">
            <nz-icon
                nzType="phone"
                nzTheme="outline"
                class="text-2xl text-text-secondary"
            />
        </app-icon-button>
    </div>
</div>
