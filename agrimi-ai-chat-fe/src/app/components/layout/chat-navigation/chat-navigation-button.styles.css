/* ng-z<PERSON><PERSON> Styles for Chat Navigation */

/* New Chat Button - Outline Variant */
/* Using multiple selectors for maximum specificity */
button.new-chat-button.ant-btn,
button.new-chat-button.ant-btn-default,
.new-chat-button.ant-btn,
.new-chat-button.ant-btn-default {
    /* Base styling to match original action-button */
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important; /* gap-2 */
    padding: 0.25rem 0.75rem !important; /* px-3 py-1 */
    border-radius: 0.5rem !important; /* rounded-button */
    transition: all 0.2s ease-in-out !important; /* transition-colors duration-200 */
    font-size: 0.875rem !important; /* text-sm */
    font-weight: 500 !important; /* font-medium */

    /* Outline variant styling - Override ng-zorro defaults */
    background-color: white !important; /* bg-white */
    background: white !important; /* Additional override */
    border: 1px solid #e5e7eb !important; /* border-neutral-lighter */
    color: #374151 !important; /* text-text-primary */

    /* Remove default ng-zorro button styling */
    box-shadow: none !important;
    height: auto !important;
    min-height: auto !important;
    line-height: normal !important;

    /* Override any ng-zorro default button colors */
    background-image: none !important;
    text-shadow: none !important;
}

/* Hover state */
.new-chat-button.ant-btn:hover,
.new-chat-button.ant-btn-default:hover {
    background-color: white !important;
    background: white !important;
    border-color: #ab85e8 !important; /* hover:border-light-purple */
    color: #374151 !important;
    box-shadow: none !important;
    background-image: none !important;
    text-shadow: none !important;
}

/* Focus state */
.new-chat-button.ant-btn:focus,
.new-chat-button.ant-btn-default:focus {
    background-color: white !important;
    background: white !important;
    border-color: #ab85e8 !important;
    color: #374151 !important;
    box-shadow: 0 0 0 2px rgba(171, 133, 232, 0.2) !important; /* focus:ring-light-purple */
    outline: none !important;
    background-image: none !important;
    text-shadow: none !important;
}

/* Active state */
.new-chat-button.ant-btn:active,
.new-chat-button.ant-btn-default:active {
    background-color: white !important;
    background: white !important;
    border-color: #ab85e8 !important;
    color: #374151 !important;
    box-shadow: 0 0 0 2px rgba(171, 133, 232, 0.2) !important;
    background-image: none !important;
    text-shadow: none !important;
}

/* Disabled state */
.new-chat-button.ant-btn[disabled],
.new-chat-button.ant-btn-default[disabled] {
    opacity: 0.5 !important;
    cursor: not-allowed !important;
    background-color: white !important;
    background: white !important;
    border-color: #e5e7eb !important;
    color: #374151 !important;
    box-shadow: none !important;
    background-image: none !important;
    text-shadow: none !important;
}

.new-chat-button.ant-btn[disabled]:hover,
.new-chat-button.ant-btn-default[disabled]:hover {
    background-color: white !important;
    background: white !important;
    border-color: #e5e7eb !important;
    color: #374151 !important;
    box-shadow: none !important;
    background-image: none !important;
    text-shadow: none !important;
}

/* Icon styling within the button */
.new-chat-button .button-icon,
.new-chat-button .button-icon .anticon {
    color: #374151 !important; /* text-text-primary */
    font-size: 1.25rem !important; /* text-xl */
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Text styling within the button */
.new-chat-button .button-text {
    font-size: 0.875rem !important; /* text-sm */
    font-weight: 500 !important; /* font-medium */
    color: #374151 !important; /* Explicit color instead of inherit */
}

/* Ensure all child elements have correct colors */
.new-chat-button * {
    color: #374151 !important;
}

/* Override any ng-zorro icon colors */
.new-chat-button .anticon {
    color: #374151 !important;
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .new-chat-button.ant-btn {
        padding: 0.25rem 0.5rem !important; /* Slightly smaller padding on mobile */
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .new-chat-button.ant-btn {
        border-width: 2px !important;
    }

    .new-chat-button.ant-btn:hover,
    .new-chat-button.ant-btn:focus {
        border-width: 2px !important;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .new-chat-button.ant-btn {
        transition: none !important;
    }
}

/* Dark mode support (if needed in the future) */
@media (prefers-color-scheme: dark) {
    .new-chat-button.ant-btn {
        background-color: #1f2937 !important;
        border-color: #374151 !important;
        color: #f9fafb !important;
    }

    .new-chat-button.ant-btn:hover {
        background-color: #1f2937 !important;
        border-color: #ab85e8 !important;
    }

    .new-chat-button .button-icon {
        color: #f9fafb !important;
    }
}

/* AGGRESSIVE OVERRIDE - Force white background and dark text */
/* This should override any ng-zorro default styling */
button[class*="new-chat-button"][class*="ant-btn"] {
    background-color: white !important;
    background: white !important;
    color: #374151 !important;
    border: 1px solid #e5e7eb !important;
    background-image: none !important;
    text-shadow: none !important;
    box-shadow: none !important;
}

button[class*="new-chat-button"][class*="ant-btn"]:hover {
    background-color: white !important;
    background: white !important;
    color: #374151 !important;
    border-color: #ab85e8 !important;
    background-image: none !important;
    text-shadow: none !important;
    box-shadow: none !important;
}

button[class*="new-chat-button"][class*="ant-btn"] * {
    color: #374151 !important;
}

/* Force icon color */
button[class*="new-chat-button"] .anticon,
button[class*="new-chat-button"] .button-icon,
button[class*="new-chat-button"] .button-text {
    color: #374151 !important;
}
