import { Component, output } from "@angular/core";
import { CommonModule } from "@angular/common";
import { IconButtonComponent } from "../../ui/icon-button/icon-button.component";
import { NzIconModule } from "ng-zorro-antd/icon";
import { NzButtonModule } from "ng-zorro-antd/button";

@Component({
    selector: "app-chat-navigation",
    imports: [CommonModule, IconButtonComponent, NzIconModule, NzButtonModule],
    templateUrl: "./chat-navigation.component.html",
    styleUrls: ["./chat-navigation-button.styles.css"],
})
export class ChatNavigationComponent {
    newChat = output<void>();
    chatHistory = output<void>();
    help = output<void>();
    phone = output<void>();

    onNewChat(): void {
        this.newChat.emit();
    }

    onChatHistory(): void {
        this.chatHistory.emit();
    }

    onHelp(): void {
        this.help.emit();
    }

    onPhone(): void {
        this.phone.emit();
    }
}
