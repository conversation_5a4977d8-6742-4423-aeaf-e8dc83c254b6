import { Component, input } from "@angular/core";
import { CommonModule } from "@angular/common";
import { Message } from "../../../models/chat.models";

@Component({
    selector: "app-user-message",
    imports: [CommonModule],
    templateUrl: "./user-message.component.html",
})
export class UserMessageComponent {
    message = input.required<Message>();

    formatTime(date: Date): string {
        return date.toLocaleTimeString("bg-BG", {
            hour: "2-digit",
            minute: "2-digit",
        });
    }
}
