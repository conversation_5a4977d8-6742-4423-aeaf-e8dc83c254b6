<div class="flex justify-start pr-10 mb-6">
    <div class="bg-white rounded-message p-4 max-w-full break-words">
        <div class="text-sm text-text-primary leading-relaxed">
            <!-- Render Text Content -->
            @for (textItem of textItems(); track $index) {
            <div class="mb-3" [innerHTML]="textItem.text | md"></div>
            }

            <!-- Render Table Content -->
            @for (tableItem of tableItems(); track $index) {
            <div class="mt-4 mb-4">
                @if (tableItem.caption) {
                <h4 class="font-medium mb-2">{{ tableItem.caption }}</h4>
                }
                <div class="overflow-x-auto">
                    <table
                        class="w-full border-collapse border border-gray-300 text-sm"
                    >
                        <thead class="bg-gray-50">
                            <tr>
                                @for (header of tableItem.headers; track $index)
                                {
                                <th
                                    class="border border-gray-300 px-3 py-2 text-left font-medium"
                                >
                                    {{ header }}
                                </th>
                                }
                            </tr>
                        </thead>
                        <tbody>
                            @for (row of tableItem.rows; track $index) {
                            <tr class="hover:bg-gray-50">
                                @for (cell of row; track $index) {
                                <td class="border border-gray-300 px-3 py-2">
                                    {{ cell }}
                                </td>
                                }
                            </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
            }

            <!-- Render List Content -->
            @for (listItem of listItems(); track $index) {
            <div class="mt-4 mb-4">
                @if (listItem.title) {
                <h4 class="font-medium mb-2">{{ listItem.title }}</h4>
                }
                <ul class="list-disc list-inside space-y-1 ml-4">
                    @for (item of listItem.items; track $index) {
                    <li class="text-sm">
                        @if (item['title'] && item['description']) {
                        <strong>{{ item["title"] }}:</strong>
                        {{ item["description"] }}
                        } @else {
                        {{
                            item["text"] ||
                                item["name"] ||
                                item["value"] ||
                                (item | json)
                        }}
                        }
                    </li>
                    }
                </ul>
            </div>
            }

            <!-- Render Quick Replies -->
            <!-- @if (quickReplies().length > 0) {
            <div class="mt-4">
                <app-quick-replies
                    [replies]="quickReplies()"
                    (replyClick)="onQuickReplyClick($event)"
                >
                </app-quick-replies>
            </div>
            } -->

            <!-- Collapsible Sources Section -->
            @if (showSources()) {
            <div class="mt-4">
                <app-collapsible-section
                    title="Sources"
                    [links]="sourceLinks()"
                    [isExpanded]="sourcesExpanded()"
                    (toggle)="onSourcesToggle()"
                    (linkClick)="onSourceClick($event)"
                >
                </app-collapsible-section>
            </div>
            }
        </div>

        <div class="text-xs text-text-secondary mt-3 opacity-70">
            {{ formatTime(message().timestamp) }}
        </div>
    </div>
</div>
