import { CommonModule } from "@angular/common";
import { Component, computed, input, output, signal } from "@angular/core";
import {
    ContentItem,
    ListContent,
    QuickRepliesContent,
    QuickReply,
    TableContent,
    TextContent,
} from "../../../models/api.models";
import { Message } from "../../../models/chat.models";
import { MDPipe } from "../../../pipes/md.pipe";
import { CollapsibleSectionComponent } from "../../ui/collapsible-section/collapsible-section.component";

@Component({
    selector: "app-bot-message",
    standalone: true,
    imports: [CommonModule, CollapsibleSectionComponent, MDPipe],
    templateUrl: "./bot-message.component.html",
})
export class BotMessageComponent {
    message = input.required<Message>();
    quickReplyClick = output<QuickReply>();

    sourcesExpanded = signal(false);

    // Check if content is structured (ContentItem[]) or simple string
    isStructuredContent = computed(() => {
        return Array.isArray(this.message().content);
    });

    // Get structured content items
    contentItems = computed((): ContentItem[] => {
        const content = this.message().content;
        if (Array.isArray(content)) {
            return content;
        }
        // If it's a string, convert to TextContent
        return [{ type: "text", text: content } as TextContent];
    });

    // Extract text content items for display
    textItems = computed(() => {
        return this.contentItems().filter(
            (item) => item.type === "text"
        ) as TextContent[];
    });

    // Extract table content items
    tableItems = computed(() => {
        return this.contentItems().filter(
            (item) => item.type === "table"
        ) as TableContent[];
    });

    // Extract list content items
    listItems = computed(() => {
        return this.contentItems().filter(
            (item) => item.type === "list"
        ) as ListContent[];
    });

    // Extract quick replies
    quickReplies = computed(() => {
        const quickReplyItems = this.contentItems().filter(
            (item) => item.type === "quick_replies"
        ) as QuickRepliesContent[];
        return quickReplyItems.length > 0 ? quickReplyItems[0].replies : [];
    });

    // Check if message has sources or other collapsible content
    showSources = computed(() => {
        // For now, no sources logic - can be added based on content metadata
        return false;
    });

    sourceLinks = computed(() => {
        // Can be populated from content metadata if needed
        return [];
    });

    formatTime(date: Date): string {
        return date.toLocaleTimeString("en-US", {
            hour: "2-digit",
            minute: "2-digit",
        });
    }

    onSourceClick(link: any): void {
        console.log("Source clicked:", link);
    }

    onSourcesToggle(): void {
        this.sourcesExpanded.set(!this.sourcesExpanded());
    }

    onQuickReplyClick(quickReply: QuickReply): void {
        this.quickReplyClick.emit(quickReply);
    }

    // Helper method to format text content with line breaks
    // formatTextContent(text: string) {
    //     return mdParse(text, { async: true });
    // }
}
