<div class="p-3 sm:p-4">
    <div
        class="flex items-center bg-white border border-neutral-lighter rounded-input shadow-input p-3 gap-3 focus-within:border-primary-teal focus-within:ring-2 focus-within:ring-primary-teal focus-within:ring-opacity-20"
    >
        <input
            #messageInput
            [(ngModel)]="message"
            (keydown.enter)="onSendMessage()"
            placeholder="Попитайте Agrimi AI"
            class="flex-1 outline-none text-sm text-text-primary placeholder-text-secondary bg-transparent"
            type="text"
            maxlength="1000"
        />

        <button
            nz-button
            nzShape="circle"
            nzSize="default"
            (click)="onSendMessage()"
            [disabled]="!message.trim()"
            class="send-button w-8 h-8 bg-light-teal text-white hover:bg-opacity-90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-light-teal focus:ring-offset-2 flex items-center justify-center border-0"
            type="button"
            aria-label="Send message"
        >
            <nz-icon nzType="send" nzTheme="outline" class="text-sm" />
        </button>
    </div>

    <div class="mt-4 text-center">
        <p class="text-xs text-text-secondary">
            ChatGPT може да допуска грешки. Проверявайте важната информация.
            <button
                class="underline hover:text-text-primary transition-colors duration-200"
                type="button"
            >
                Научете повече.
            </button>
        </p>
    </div>
</div>
