import { Component, output, ViewChild, ElementRef } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";
import { NzIconModule } from "ng-zorro-antd/icon";
import { NzButtonModule } from "ng-zorro-antd/button";

@Component({
    selector: "app-chat-input",
    imports: [CommonModule, FormsModule, NzIconModule, NzButtonModule],
    templateUrl: "./chat-input.component.html",
    styles: [
        `
            .send-button.ant-btn {
                width: 2rem !important;
                height: 2rem !important;
                min-width: 2rem !important;
                padding: 0 !important;
                border: none !important;
                border-radius: 50% !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }

            .send-button.ant-btn:hover {
                border: none !important;
            }

            .send-button.ant-btn:focus {
                border: none !important;
            }

            .send-button.ant-btn[disabled] {
                border: none !important;
            }
        `,
    ],
})
export class ChatInputComponent {
    @ViewChild("messageInput") messageInput!: ElementRef<HTMLInputElement>;
    sendMessage = output<string>();

    message = "";

    onSendMessage(): void {
        const trimmedMessage = this.message.trim();
        if (trimmedMessage) {
            this.sendMessage.emit(trimmedMessage);
            this.message = "";
            this.messageInput.nativeElement.focus();
        }
    }
}
