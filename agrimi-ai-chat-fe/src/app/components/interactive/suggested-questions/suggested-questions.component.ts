import { Component, input, output } from "@angular/core";
import { CommonModule } from "@angular/common";
import { SuggestedQuestion } from "../../../models/chat.models";
import { NzIconModule } from "ng-zorro-antd/icon";

@Component({
    selector: "app-suggested-questions",
    imports: [CommonModule, NzIconModule],
    templateUrl: "./suggested-questions.component.html",
})
export class SuggestedQuestionsComponent {
    questions = input<SuggestedQuestion[]>([]);
    questionClick = output<SuggestedQuestion>();

    onQuestionClick(question: SuggestedQuestion): void {
        this.questionClick.emit(question);
    }
}
