<div class="flex flex-col gap-2 p-3 sm:p-4">
    @for (question of questions(); track question.id) {
    <button
        (click)="onQuestionClick(question)"
        class="suggestion-button text-left group"
        type="button"
    >
        <div class="w-6 h-6 flex-shrink-0 text-light-purple">
            <nz-icon
                nzType="ai-icon:app"
                nzTheme="outline"
                class="text-light-purple"
            />
        </div>
        <span
            class="flex-1 text-sm text-text-primary group-hover:text-light-purple transition-colors duration-200"
        >
            {{ question.text }}
        </span>
    </button>
    }
</div>
