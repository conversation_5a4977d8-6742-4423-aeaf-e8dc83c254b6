import { Injectable, inject } from '@angular/core';
import { APP_CONFIG, AppConfig } from '../config/app-config.token';

/**
 * Configuration service that provides access to application configuration
 * This service demonstrates how to inject and use the APP_CONFIG token
 */
@Injectable({
  providedIn: 'root'
})
export class ConfigService {
  private readonly config = inject(APP_CONFIG);

  /**
   * Get the complete application configuration
   */
  getConfig(): AppConfig {
    return this.config;
  }

  /**
   * Get the API base URL
   */
  getApiUrl(): string {
    return this.config.apiUrl;
  }

  /**
   * Get the application name
   */
  getAppName(): string {
    return this.config.appName;
  }

  /**
   * Get the application version
   */
  getVersion(): string {
    return this.config.version;
  }

  /**
   * Check if the application is running in production mode
   */
  isProduction(): boolean {
    return this.config.production;
  }

  /**
   * Check if analytics is enabled
   */
  isAnalyticsEnabled(): boolean {
    return this.config.features.enableAnalytics;
  }

  /**
   * Check if logging is enabled
   */
  isLoggingEnabled(): boolean {
    return this.config.features.enableLogging;
  }

  /**
   * Check if offline mode is enabled
   */
  isOfflineModeEnabled(): boolean {
    return this.config.features.enableOfflineMode;
  }

  /**
   * Get chat configuration
   */
  getChatConfig() {
    return this.config.chat;
  }

  /**
   * Get UI configuration
   */
  getUIConfig() {
    return this.config.ui;
  }

  /**
   * Get maximum message length for chat
   */
  getMaxMessageLength(): number {
    return this.config.chat.maxMessageLength;
  }

  /**
   * Get typing indicator delay
   */
  getTypingIndicatorDelay(): number {
    return this.config.chat.typingIndicatorDelay;
  }

  /**
   * Get auto-scroll delay
   */
  getAutoScrollDelay(): number {
    return this.config.chat.autoScrollDelay;
  }

  /**
   * Get current theme setting
   */
  getTheme(): 'light' | 'dark' | 'auto' {
    return this.config.ui.theme;
  }

  /**
   * Get current language setting
   */
  getLanguage(): string {
    return this.config.ui.language;
  }

  /**
   * Check if animations are enabled
   */
  areAnimationsEnabled(): boolean {
    return this.config.ui.animations;
  }

  /**
   * Update configuration (for runtime configuration changes)
   * Note: This creates a new configuration object since the injected config is readonly
   */
  updateConfig(updates: Partial<AppConfig>): AppConfig {
    return { ...this.config, ...updates };
  }

  /**
   * Log configuration information (useful for debugging)
   */
  logConfig(): void {
    if (this.isLoggingEnabled()) {
      console.group('🔧 Application Configuration');
      console.log('Environment:', this.isProduction() ? 'Production' : 'Development');
      console.log('App Name:', this.getAppName());
      console.log('Version:', this.getVersion());
      console.log('API URL:', this.getApiUrl());
      console.log('Features:', this.config.features);
      console.log('Chat Config:', this.config.chat);
      console.log('UI Config:', this.config.ui);
      console.groupEnd();
    }
  }
}

/**
 * Example usage in a component:
 * 
 * @Component({...})
 * export class SomeComponent {
 *   private configService = inject(ConfigService);
 * 
 *   ngOnInit() {
 *     console.log('API URL:', this.configService.getApiUrl());
 *     console.log('Max message length:', this.configService.getMaxMessageLength());
 *     
 *     if (this.configService.isLoggingEnabled()) {
 *       this.configService.logConfig();
 *     }
 *   }
 * }
 */
