import { InjectionToken } from '@angular/core';

/**
 * Application configuration interface
 * Define the structure of your application configuration
 */
export interface AppConfig {
  production: boolean;
  apiUrl: string;
  appName: string;
  version: string;
  features: {
    enableAnalytics: boolean;
    enableLogging: boolean;
    enableOfflineMode: boolean;
  };
  chat: {
    maxMessageLength: number;
    typingIndicatorDelay: number;
    autoScrollDelay: number;
  };
  ui: {
    theme: 'light' | 'dark' | 'auto';
    language: string;
    animations: boolean;
  };
}

/**
 * Injection token for application configuration
 * Use this token to inject configuration throughout your application
 */
export const APP_CONFIG = new InjectionToken<AppConfig>('app.config', {
  providedIn: 'root',
  factory: () => ({
    production: false,
    apiUrl: 'http://localhost:8003/api',
    appName: 'Agrimi AI Chat',
    version: '1.0.0',
    features: {
      enableAnalytics: false,
      enableLogging: true,
      enableOfflineMode: false,
    },
    chat: {
      maxMessageLength: 1000,
      typingIndicatorDelay: 500,
      autoScrollDelay: 100,
    },
    ui: {
      theme: 'auto',
      language: 'bg',
      animations: true,
    },
  })
});

/**
 * Environment-specific configurations
 */
export const DEVELOPMENT_CONFIG: AppConfig = {
  production: false,
  apiUrl: 'http://localhost:8003/api',
  appName: 'Agrimi AI Chat (Dev)',
  version: '1.0.0-dev',
  features: {
    enableAnalytics: false,
    enableLogging: true,
    enableOfflineMode: false,
  },
  chat: {
    maxMessageLength: 1000,
    typingIndicatorDelay: 500,
    autoScrollDelay: 100,
  },
  ui: {
    theme: 'auto',
    language: 'bg',
    animations: true,
  },
};

export const PRODUCTION_CONFIG: AppConfig = {
  production: true,
  apiUrl: 'https://api.agrimi.ai',
  appName: 'Agrimi AI Chat',
  version: '1.0.0',
  features: {
    enableAnalytics: true,
    enableLogging: false,
    enableOfflineMode: true,
  },
  chat: {
    maxMessageLength: 1000,
    typingIndicatorDelay: 500,
    autoScrollDelay: 100,
  },
  ui: {
    theme: 'auto',
    language: 'bg',
    animations: true,
  },
};

/**
 * Additional injection tokens for specific features
 */
export const API_BASE_URL = new InjectionToken<string>('api.base.url');
export const CHAT_CONFIG = new InjectionToken<AppConfig['chat']>('chat.config');
export const UI_CONFIG = new InjectionToken<AppConfig['ui']>('ui.config');

/**
 * Factory function for creating configuration based on environment
 */
export function createAppConfig(environment: 'development' | 'production'): AppConfig {
  return environment === 'production' ? PRODUCTION_CONFIG : DEVELOPMENT_CONFIG;
}

/**
 * Example usage in a service:
 * 
 * @Injectable()
 * export class SomeService {
 *   constructor(@Inject(APP_CONFIG) private config: AppConfig) {
 *     console.log('API URL:', this.config.apiUrl);
 *   }
 * }
 * 
 * Or using the inject function:
 * 
 * @Injectable()
 * export class SomeService {
 *   private config = inject(APP_CONFIG);
 *   
 *   someMethod() {
 *     console.log('App Name:', this.config.appName);
 *   }
 * }
 */
