import { Routes } from "@angular/router";

/**
 * Application routes configuration
 * Define your application routes here using the standalone routing approach
 */
export const routes: Routes = [
    // Default route
    {
        path: "",
        redirectTo: "/chat",
        pathMatch: "full",
    },

    // Chat route (main application route)
    {
        path: "chat",
        loadComponent: () =>
            import("./app/app.component").then((m) => m.AppComponent),
        title: "Agrimi AI Chat",
    },

    // Example: Lazy-loaded feature routes
    // {
    //   path: 'admin',
    //   loadChildren: () => import('./features/admin/admin.routes').then(m => m.adminRoutes),
    //   canActivate: [AuthGuard],
    //   data: { roles: ['admin'] }
    // },

    // Example: Component route with parameters
    // {
    //   path: 'user/:id',
    //   loadComponent: () => import('./features/user/user.component').then(m => m.UserComponent),
    //   title: 'User Profile'
    // },

    // Example: Route with providers (scoped services)
    // {
    //   path: 'feature',
    //   loadComponent: () => import('./features/feature/feature.component').then(m => m.FeatureComponent),
    //   providers: [
    //     FeatureService,
    //     { provide: FEATURE_CONFIG, useValue: { setting: 'value' } }
    //   ]
    // },

    // Example: Route with guards and resolvers
    // {
    //   path: 'protected',
    //   loadComponent: () => import('./features/protected/protected.component').then(m => m.ProtectedComponent),
    //   canActivate: [AuthGuard],
    //   canDeactivate: [UnsavedChangesGuard],
    //   resolve: {
    //     data: DataResolver
    //   }
    // },

    // Wildcard route - must be last
    // {
    //   path: '**',
    //   loadComponent: () => import('./shared/components/not-found/not-found.component').then(m => m.NotFoundComponent),
    //   title: 'Page Not Found'
    // }
];

/**
 * Example: Feature-specific routes that can be imported
 * You can create separate route files for different features
 */
// export const chatRoutes: Routes = [
//   {
//     path: '',
//     loadComponent: () => import('./components/layout/chat-container/chat-container.component').then(m => m.ChatContainerComponent)
//   },
//   {
//     path: 'history',
//     loadComponent: () => import('./components/chat-history/chat-history.component').then(m => m.ChatHistoryComponent)
//   }
// ];
