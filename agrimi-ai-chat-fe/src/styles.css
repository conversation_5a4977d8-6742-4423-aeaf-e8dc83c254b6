@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    * {
        box-sizing: border-box;
    }

    body {
        margin: 0;
        padding: 0;
        overflow-x: hidden;
    }
}

@layer components {
    .message-bubble {
        @apply rounded-message p-3 sm:p-4 break-words;
    }

    .user-message {
        @apply message-bubble bg-chat-user-bg ml-4 sm:ml-10 rounded-tr-none;
    }

    .bot-message {
        @apply message-bubble bg-white mr-4 sm:mr-10;
    }

    .suggestion-button {
        @apply flex items-center gap-2 p-2 sm:p-3 rounded-button bg-suggestion-bg border border-transparent hover:border-light-purple transition-colors duration-200;
    }

    .icon-button {
        @apply flex items-center justify-center w-8 h-8 p-1 rounded-button;
    }

    .link-button {
        @apply flex items-center gap-2 px-2 sm:px-3 py-1 rounded-button bg-neutral-light border border-neutral-light text-primary-teal text-sm font-medium;
    }

    .chat-container {
        @apply flex flex-col w-full max-w-sm sm:max-w-md lg:max-w-lg mx-auto bg-white rounded-none sm:rounded-2xl shadow-none sm:shadow-lg overflow-hidden;
        height: 100vh;
    }

    @screen sm {
        .chat-container {
            height: 600px;
        }
    }

    @screen md {
        .chat-container {
            height: 800px;
        }
    }
}

@layer utilities {
    .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
    }

    .scrollbar-hide::-webkit-scrollbar {
        display: none;
    }
}
