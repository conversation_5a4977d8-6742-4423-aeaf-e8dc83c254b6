import { AppConfig } from "../app/core/config/app-config.token";

export const environment: AppConfig = {
    production: true,
    apiUrl: "https://api.agrimi.ai",
    appName: "Agrimi AI Chat",
    version: "1.0.0",
    features: {
        enableAnalytics: true,
        enableLogging: false,
        enableOfflineMode: true,
    },
    chat: {
        maxMessageLength: 1000,
        typingIndicatorDelay: 500,
        autoScrollDelay: 100,
    },
    ui: {
        theme: "auto",
        language: "bg",
        animations: true,
    },
};

export default environment;
