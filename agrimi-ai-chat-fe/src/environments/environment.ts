import { AppConfig } from "../app/core/config/app-config.token";

export const environment: AppConfig = {
    production: false,
    apiUrl: "http://localhost:8003/api",
    appName: "Agrimi AI Chat (Development)",
    version: "1.0.0-dev",
    features: {
        enableAnalytics: false,
        enableLogging: true,
        enableOfflineMode: false,
    },
    chat: {
        maxMessageLength: 1000,
        typingIndicatorDelay: 500,
        autoScrollDelay: 100,
    },
    ui: {
        theme: "auto",
        language: "bg",
        animations: true,
    },
};

export default environment;
