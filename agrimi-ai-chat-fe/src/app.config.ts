import { ApplicationConfig } from "@angular/core";
import {
    provideRouter,
    withComponentInputBinding,
    withEnabledBlockingInitialNavigation,
} from "@angular/router";
import { provideHttpClient } from "@angular/common/http";
import { provideAnimations } from "@angular/platform-browser/animations";

// Import your routes
import { routes } from "./app.routes";

// Import environment configuration
import { environment } from "./environments/environment";

// Import injection tokens and configuration
import { APP_CONFIG } from "./app/core/config/app-config.token";
import {
    ExpandAltOutline,
    CloseOutline,
    QuestionCircleOutline,
    PlusOutline,
    PhoneOutline,
    ArrowUpOutline,
    MessageOutline,
    FileTextOutline,
    SendOutline,
} from "@ant-design/icons-angular/icons";

import { provideNzIcons } from "ng-zorro-antd/icon";

/**
 * Application configuration for Angular standalone bootstrap
 * This configuration replaces the traditional NgModule approach
 */
export const appConfig: ApplicationConfig = {
    providers: [
        // Router configuration
        provideRouter(
            routes,
            withComponentInputBinding(),
            withEnabledBlockingInitialNavigation()
        ),

        // HTTP Client configuration
        provideHttpClient(),

        // Browser animations
        provideAnimations(),

        { provide: APP_CONFIG, useValue: environment },

        // Ant Design Icons
        provideNzIcons([
            ExpandAltOutline,
            CloseOutline,
            QuestionCircleOutline,
            PlusOutline,
            PhoneOutline,
            ArrowUpOutline,
            MessageOutline,
            FileTextOutline,
            SendOutline,
        ]),
    ],
};
