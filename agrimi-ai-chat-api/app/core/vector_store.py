from llama_index.vector_stores.postgres import PGVectorStore
from llama_index.core import VectorStoreIndex, StorageContext
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.core.vector_stores import VectorStoreQuery
from sqlalchemy import text
import asyncio
from typing import List
from app.core.config import settings
from sqlalchemy import create_engine
from sqlalchemy.engine.url import make_url
import logging

logging.getLogger("llama_index").setLevel(logging.DEBUG)
logging.getLogger("llama_index.vector_stores").setLevel(logging.DEBUG)


class HybridVectorStore:
    def __init__(self, db_url: str):
        url = make_url(db_url)

        # Use the existing document_chunks table
        self.vector_store = PGVectorStore.from_params(
            database="db_bg_5432",
            host=url.host,
            password=url.password,
            port=url.port,
            user=url.username,
            embed_dim=1536,
            table_name="document_chunks",
            hybrid_search=True,
            text_search_config="bulgarian",
        )

        self.embed_model = OpenAIEmbedding(
            model=settings.embedding_model, api_key=settings.openai_api_key
        )

    async def hybrid_search(self, query: str, top_k: int = 5, alpha: float = 0.5):
        """Perform hybrid search combining vector and keyword search"""
        try:
            # Check if vector store is available
            if not self.vector_store:
                print("Vector store not available, using fallback search")
                return await self._fallback_search(query, top_k)
            # Validate parameters
            if not isinstance(top_k, int) or top_k <= 0:
                top_k = 5

            if not isinstance(alpha, (int, float)) or alpha < 0 or alpha > 1:
                alpha = 0.5

            # Get query embedding
            query_embedding = await self.embed_model.aget_query_embedding(query)
            # print(f"Query embedding: {query_embedding}")

            # Create vector store query
            vector_query = VectorStoreQuery(
                query_embedding=query_embedding,
                similarity_top_k=int(top_k),  # Ensure it's an integer
                mode="default",
            )

            # Perform search
            results = self.vector_store.query(vector_query)

            return results.nodes if hasattr(results, "nodes") and results.nodes else []

        except Exception as e:
            print(f"Search error: {e}")
            print("Falling back to direct PostgreSQL search")
            import traceback

            traceback.print_exc()
            # Fall back to direct PostgreSQL search
            # return await self._fallback_search(query, top_k)

    async def _fallback_search(self, query: str, top_k: int = 5):
        """Fallback search using direct PostgreSQL queries"""
        try:
            import psycopg2
            from psycopg2.extras import RealDictCursor

            # Convert connection string
            db_url = settings.database_url

            conn = psycopg2.connect(db_url)
            results = []

            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                # Simple text search in document_chunks table
                cur.execute(
                    """
                    SELECT content, metadata, document_id
                    FROM document_chunks 
                    WHERE content ILIKE %s 
                    ORDER BY created_at DESC 
                    LIMIT %s
                """,
                    (f"%{query}%", top_k),
                )

                rows = cur.fetchall()

                # Convert to a simple node-like structure
                for row in rows:
                    # Create a simple object that mimics LlamaIndex node structure
                    class SimpleNode:
                        def __init__(self, text, metadata):
                            self.text = text
                            self.content = text
                            self.metadata = metadata or {}
                            self.score = 0.5  # Default score

                    results.append(SimpleNode(row["content"], row["metadata"]))

            conn.close()
            print(f"✅ Fallback search returned {len(results)} results")
            return results

        except Exception as e:
            print(f"Fallback search error: {e}")
            return []

    def get_storage_context(self):
        """Get storage context for indexing"""
        if self.vector_store:
            return StorageContext.from_defaults(vector_store=self.vector_store)
        else:
            # Return default storage context without vector store
            return StorageContext.from_defaults()

    async def create_index_from_documents(self, documents):
        """Create or update index from documents"""
        if not self.vector_store:
            print("Vector store not available, skipping indexing")
            return None

        try:
            storage_context = self.get_storage_context()
            index = VectorStoreIndex.from_documents(
                documents, storage_context=storage_context, embed_model=self.embed_model
            )
            return index
        except Exception as e:
            print(f"Error creating index: {e}")
            return None
