from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    # App
    log_level: str = "info"
    # Database
    database_url: str = (
        "postgresql://postgres:6nuk23@localhost:5432/db_bg_5432?charset=utf8"
    )
    redis_url: str = "redis://localhost:6379"

    # OpenAI
    openai_api_key: str
    embedding_model: str = "text-embedding-3-small"
    llm_model: str = "gpt-4.1-nano-2025-04-14"
    # Google
    google_api_key: str

    # RAG settings
    chunk_size: int = 1024
    chunk_overlap: int = 128
    top_k: int = 2
    hybrid_search_alpha: float = 0.5  # Balance between vector and keyword

    # Conversation
    max_conversation_history: int = 10
    conversation_ttl: int = 3600  # 1 hour in Redis

    class Config:
        env_file = ".env"


settings = Settings()
