import time
from llama_index.core import ServiceContext
from llama_index.core.llms.function_calling import FunctionCallingLLM

from typing import List, Dict
import json
from app.core.config import settings
from app.core.vector_store import HybridVectorStore


class RAGEngine:
    def __init__(self, vector_store: HybridVectorStore, llm: FunctionCallingLLM):
        self.vector_store = vector_store
        self.llm = llm

    async def query_with_context(
        self, query: str, conversation_history: List[Dict], session_id: str
    ) -> Dict:
        # Enhance query with conversation context
        context_query = self._build_contextual_query(query, conversation_history)

        # Perform hybrid search
        search_results = await self.vector_store.hybrid_search(
            context_query, top_k=settings.top_k, alpha=settings.hybrid_search_alpha
        )

        # Format context from search results
        context = self._format_context(search_results)

        # Generate response
        response = await self._generate_response(query, context, conversation_history)

        return response

    def _build_contextual_query(
        self, query: str, conversation_history: List[Dict]
    ) -> str:
        """Enhance query with conversation context"""
        if not conversation_history:
            return query

        # Get last few messages for context
        recent_messages = (
            conversation_history[-3:]
            if len(conversation_history) >= 3
            else conversation_history
        )
        context_parts = []

        for msg in recent_messages:
            role = msg.get("role", "")
            content = msg.get("content", "")
            if role and content:
                context_parts.append(f"{role}: {content}")

        if context_parts:
            context_str = "\n".join(context_parts)
            return f"Previous conversation:\n{context_str}\n\nCurrent question: {query}"

        return query

    def _format_context(self, search_results) -> Dict:
        """Format search results with source information"""
        context_parts = []
        sources = []

        if not search_results:
            return {
                "context": "No relevant context found in the knowledge base.",
                "sources": [],
            }

        for i, result in enumerate(search_results):
            try:
                text = (
                    getattr(result, "text", "")
                    or getattr(result, "content", "")
                    or str(result)
                )
                context_parts.append(f"[Source {i+1}]: {text}")

                metadata = getattr(result, "metadata", {}) or {}
                score = getattr(result, "score", 0.0)

                sources.append(
                    {
                        "index": i + 1,
                        "document": metadata.get("document_id", "Unknown"),
                        "section": metadata.get("section", ""),
                        "score": score,
                    }
                )
            except Exception as e:
                print(f"Error processing search result {i}: {e}")
                continue

        return {
            "context": (
                "\n\n".join(context_parts)
                if context_parts
                else "No relevant context found."
            ),
            "sources": sources,
        }

    async def _generate_response(
        self, query: str, context: Dict, conversation_history: List[Dict]
    ) -> Dict:
        """Generate response using LLM with context"""
        # Build prompt with context and conversation history
        system_prompt = """You are a helpful AI assistant. Use the provided context to answer the user's question. 
        If the context doesn't contain relevant information, say so clearly. 
        Always cite your sources when providing information from the context."""

        user_prompt = f"""Context:
{context['context']}

Question: {query}

Please provide a helpful response based on the context above."""
        # Generate response using LLM
        timestart = time.time()
        print(f"Generating response... timestart: {timestart}")
        response = await self.llm.acomplete(user_prompt)
        print(f"Response generated... timeend: {time.time()-timestart}")

        return {
            "text": str(response),
            "sources": context["sources"],
            "context": context,
            "query_type": "general",
        }
