import redis.asyncio as redis
import json
from typing import List, Dict, Optional
from app.core.config import settings


class ConversationManager:
    def __init__(self, redis_url: str):
        self.redis_url = redis_url
        self.redis_client = None

    async def connect(self):
        """Initialize Redis connection"""
        self.redis_client = redis.from_url(self.redis_url)

    async def close(self):
        """Close Redis connection"""
        if self.redis_client:
            await self.redis_client.close()

    async def get_conversation(self, session_id: str) -> Dict:
        """Get conversation history for a session"""
        if not self.redis_client:
            await self.connect()

        conversation_key = f"conversation:{session_id}"
        conversation_data = await self.redis_client.get(conversation_key)

        if conversation_data:
            return json.loads(conversation_data)
        else:
            return {"messages": [], "created_at": None}

    async def add_message(self, session_id: str, role: str, content: str):
        """Add a message to the conversation history"""
        if not self.redis_client:
            await self.connect()

        conversation_key = f"conversation:{session_id}"
        conversation = await self.get_conversation(session_id)

        # Add new message
        message = {
            "role": role,
            "content": content,
            "timestamp": None,  # You might want to add actual timestamp
        }

        conversation["messages"].append(message)

        # Keep only the last N messages to prevent memory bloat
        if len(conversation["messages"]) > settings.max_conversation_history:
            conversation["messages"] = conversation["messages"][
                -settings.max_conversation_history :
            ]

        # Store back in Redis with TTL
        await self.redis_client.setex(
            conversation_key, settings.conversation_ttl, json.dumps(conversation)
        )

    async def clear_conversation(self, session_id: str):
        """Clear conversation history for a session"""
        if not self.redis_client:
            await self.connect()

        conversation_key = f"conversation:{session_id}"
        await self.redis_client.delete(conversation_key)

    async def get_session_info(self, session_id: str) -> Dict:
        """Get session information"""
        conversation = await self.get_conversation(session_id)
        return {
            "session_id": session_id,
            "message_count": len(conversation["messages"]),
            "created_at": conversation.get("created_at"),
            "last_activity": None,  # You might want to track this
        }
