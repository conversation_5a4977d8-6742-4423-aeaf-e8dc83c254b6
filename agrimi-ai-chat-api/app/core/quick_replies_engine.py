import json
from typing import List, Dict
from llama_index.core.llms.function_calling import FunctionCallingLLM

from app.api.models import QuickReply
from app.core.config import settings


class QuickRepliesEngine:
    def __init__(self, llm: FunctionCallingLLM):
        self.llm = llm

    async def generate_quick_replies(
        self, response_text: str, context: Dict
    ) -> List[QuickReply]:
        """Generate mix of predefined and dynamic quick replies"""

        # Generate contextual follow-ups using LLM
        quick_replies = await self._generate_dynamic_replies(response_text, context)

        return quick_replies[:4]  # Limit to 4 quick replies

    async def _generate_dynamic_replies(
        self, response_text: str, context: Dict
    ) -> List[QuickReply]:
        """Use LLM to generate contextual follow-up questions"""
        prompt = f"""Based on this response: "{response_text[:200]}..."
        Generate 2 natural follow-up questions a user might ask.
        Use Bulgarian language.
        Create a json array and return it as text, not as markdown: "[{{"title"="question", "payload"="payload_key"}}]"
        Example: "[{{"title":"Какво е това?", "payload":"what_is_this"}}, 
            {{"title":"Колко струва?", "payload":"how_much_does_it_cost"}}]"
        
        Keep questions concise and relevant to the response."""

        try:
            # Call LLM to generate suggestions
            response = await self.llm.acomplete(prompt)
            suggestions = str(response)

            replies = []
            suggestions_arr = json.loads(suggestions)
            for suggestion in suggestions_arr:
                replies.append(
                    QuickReply(title=suggestion["title"], payload=suggestion["payload"])
                )

            return replies
        except Exception as e:
            # Fallback to predefined replies if LLM fails
            return []
