from llama_index.llms.openai import OpenAI
from llama_index.llms.gemini import Gemini
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import logging

from app.api import chat
from app.core.config import settings
from app.core.vector_store import HybridVectorStore
from app.core.rag_engine import RAGEngine
from app.core.conversation_manager import ConversationManager
from app.core.quick_replies_engine import QuickRepliesEngine
import sys


# Set up logging
logging.basicConfig(
    format="%(asctime)s %(levelname)-8s %(message)s",
    level=settings.log_level.upper(),
    datefmt="%Y-%m-%d %H:%M:%S",
)
# Or specifically for LlamaIndex


logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger.info("Starting up AI Chatbot API...")

    try:
        # Initialize vector store
        vector_store = HybridVectorStore(settings.database_url)
        llm = Gemini(
            model=settings.llm_model, api_key=settings.google_api_key, temperature=0.7
        )
        # Initialize engines
        chat.rag_engine = RAGEngine(vector_store, llm)
        chat.conversation_manager = ConversationManager(settings.redis_url)
        chat.quick_replies_engine = QuickRepliesEngine(llm)

        # Connect to Redis
        await chat.conversation_manager.connect()

        logger.info("All services initialized successfully")

    except Exception as e:
        logger.error(f"Failed to initialize services: {e}")
        raise

    yield

    # Shutdown
    logger.info("Shutting down AI Chatbot API...")
    if chat.conversation_manager:
        await chat.conversation_manager.close()
    logger.info("Shutdown complete")


app = FastAPI(
    title="Agrimi AI Chatbot API",
    description="AI-powered chatbot with RAG capabilities using LlamaIndex and FastAPI",
    version="1.0.0",
    lifespan=lifespan,
)

# CORS for Angular frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:4200",
        "http://127.0.0.1:4200",
    ],  # Angular dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(chat.router, prefix="/api", tags=["chat"])


@app.get("/")
async def root():
    return {"message": "Agrimi AI Chatbot API", "version": "1.0.0", "status": "running"}


@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "agrimi-ai-chatbot-api", "version": "1.0.0"}


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "app.main:app", host="0.0.0.0", port=8003, reload=True, log_level="info"
    )
