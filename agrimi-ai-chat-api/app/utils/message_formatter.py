from typing import List, Dict, Optional
from app.api.models import (
    ChatMessage, Message, TextContent, QuickRepliesContent, 
    TableContent, ListContent, QuickReply
)


class MessageFormatter:
    @staticmethod
    def format_response(
        text: str,
        sources: List[Dict],
        quick_replies: List[QuickReply],
        tables: Optional[List[Dict]] = None
    ) -> ChatMessage:
        """Format response into the required message structure"""
        content = []
        
        # Add main text response
        content.append(TextContent(type="text", text=text))
        
        # Add sources as a list
        if sources:
            source_items = []
            for source in sources:
                source_items.append({
                    "title": f"Source {source['index']}: {source['document']}",
                    "link": f"#source-{source['index']}",
                    "relevance": f"{source['score']:.2f}"
                })
            
            content.append(ListContent(
                type="list",
                title="Sources",
                items=source_items
            ))
        
        # Add tables if present
        if tables:
            for table in tables:
                content.append(TableContent(
                    type="table",
                    headers=table["headers"],
                    rows=table["rows"],
                    caption=table.get("caption")
                ))
        
        # Add quick replies
        if quick_replies:
            content.append(QuickRepliesContent(
                type="quick_replies",
                replies=quick_replies
            ))
        
        return ChatMessage(
            message=Message(
                role="assistant",
                content=content
            )
        )