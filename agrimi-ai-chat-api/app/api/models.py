from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional, Literal, Union
from enum import Enum


class ContentType(str, Enum):
    TEXT = "text"
    QUICK_REPLIES = "quick_replies"
    TABLE = "table"
    LIST = "list"


class QuickReply(BaseModel):
    title: str
    payload: str


class TextContent(BaseModel):
    type: Literal["text"]
    text: str


class QuickRepliesContent(BaseModel):
    type: Literal["quick_replies"]
    replies: List[QuickReply]


class TableContent(BaseModel):
    type: Literal["table"]
    headers: List[str]
    rows: List[List[str]]
    caption: Optional[str] = None


class ListContent(BaseModel):
    type: Literal["list"]
    items: List[Dict[str, Any]]
    title: Optional[str] = None


ContentItem = Union[TextContent, QuickRepliesContent, TableContent, ListContent]


class Message(BaseModel):
    role: Literal["user", "assistant"]
    content: List[ContentItem]


class ChatMessage(BaseModel):
    message: Message


class ChatRequest(BaseModel):
    query: str
    session_id: str
    payload: Optional[str] = None  # For quick reply payloads