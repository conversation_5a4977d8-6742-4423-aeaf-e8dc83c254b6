from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse
from sse_starlette.sse import EventSourceResponse
import json
import asyncio
from typing import AsyncGenerator, Optional

from app.api.models import ChatRequest
from app.core.rag_engine import RAGEngine
from app.core.conversation_manager import ConversationManager
from app.core.quick_replies_engine import QuickRepliesEngine
from app.utils.message_formatter import MessageFormatter
from app.core.config import settings

router = APIRouter()

# Global instances - will be initialized in main.py
rag_engine: RAGEngine = None
conversation_manager: ConversationManager = None
quick_replies_engine: QuickRepliesEngine = None


@router.get("/chat/stream")
async def stream_chat(query: str, session_id: str, payload: Optional[str] = None):
    """SSE endpoint for streaming chat responses"""

    async def event_generator() -> AsyncGenerator[str, None]:
        try:
            # Get conversation history
            conversation = await conversation_manager.get_conversation(session_id)

            # Process query through RAG engine
            response_data = await rag_engine.query_with_context(
                query=query,
                conversation_history=conversation["messages"],
                session_id=session_id,
            )

            # Generate quick replies
            quick_replies = await quick_replies_engine.generate_quick_replies(
                response_text=response_data["text"], context=response_data["context"]
            )

            # Format message
            message = MessageFormatter.format_response(
                text=response_data["text"],
                sources=[],
                quick_replies=quick_replies,
                tables=response_data.get("tables"),
            )

            # Stream the response
            yield f"event: message\ndata: {json.dumps(message.model_dump())}\n\n"

            # Update conversation history
            await conversation_manager.add_message(
                session_id=session_id, role="user", content=query
            )
            await conversation_manager.add_message(
                session_id=session_id,
                role="assistant",
                content=response_data["text"],
            )

            # Send completion event
            yield f"event: done\ndata: {json.dumps({'status': 'completed'})}\n\n"

        except Exception as e:
            yield f"event: error\ndata: {json.dumps({'error': str(e)})}\n\n"

    return StreamingResponse(event_generator(), media_type="text/event-stream")


@router.post("/chat/quick-reply")
async def handle_quick_reply(payload: str, session_id: str):
    """Handle quick reply payload clicks"""
    # Define payload handlers
    payload_handlers = {
        "install_guide": "How do I install the software?",
        "quick_start": "Show me a quick start tutorial",
        "requirements": "What are the system requirements?",
        "common_errors": "What are the most common errors?",
        "debug_guide": "How do I debug issues?",
        "support": "How can I get support?",
        "more_info": "Can you provide more information about this?",
        "show_examples": "Can you show me some examples?",
        "how_it_works": "How does this work?",
        "benefits": "What are the benefits?",
        "configure_guide": "How do I configure this?",
    }

    # Convert payload to query
    query = payload_handlers.get(payload, payload)

    # Reuse the stream_chat logic with new parameter format
    return await stream_chat(query=query, session_id=session_id, payload=payload)


@router.get("/chat/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "chat-api"}


@router.delete("/chat/session/{session_id}")
async def clear_session(session_id: str):
    """Clear conversation history for a session"""
    await conversation_manager.clear_conversation(session_id)
    return {"status": "session_cleared", "session_id": session_id}


@router.get("/chat/session/{session_id}/info")
async def get_session_info(session_id: str):
    """Get session information"""
    session_info = await conversation_manager.get_session_info(session_id)
    return session_info
