Perfect! Based on your requirements, I'll create a comprehensive POC plan. For the payload handling, I recommend using payloads as **intent identifiers** that can trigger either: (1) predefined queries, (2) follow-up questions with context, or (3) navigation to specific document sections. This gives you flexibility.

## LlamaIndex + FastAPI + SSE POC Development Plan

### Phase 1: Core Infrastructure Setup (Days 1-2)

**TODO 1.1: Project Structure Setup**
```bash
rag-chatbot-poc/
├── app/
│   ├── __init__.py
│   ├── main.py
│   ├── api/
│   │   ├── __init__.py
│   │   ├── chat.py
│   │   ├── models.py
│   │   └── middleware.py
│   ├── core/
│   │   ├── __init__.py
│   │   ├── config.py
│   │   ├── rag_engine.py
│   │   ├── vector_store.py
│   │   ├── conversation_manager.py
│   │   └── quick_replies_engine.py
│   ├── utils/
│   │   ├── __init__.py
│   │   ├── document_processor.py
│   │   ├── message_formatter.py
│   │   └── table_formatter.py
│   └── db/
│       ├── __init__.py
│       ├── models.py
│       └── session.py
├── documents/
├── tests/
├── docker-compose.yml
├── requirements.txt
├── .env.example
└── README.md
```

**TODO 1.2: Dependencies Installation**
```bash
# Core dependencies
pip install llama-index==0.10.37
pip install llama-index-vector-stores-postgres
pip install llama-index-embeddings-openai

# FastAPI + SSE
pip install fastapi==0.111.0
pip install uvicorn[standard]
pip install sse-starlette==2.0.0

# Database
pip install pgvector==0.2.5
pip install asyncpg==0.29.0
pip install sqlalchemy==2.0.30
pip install alembic

# Utils
pip install pydantic==2.7.1
pip install python-dotenv==1.0.1
pip install pypdf2==3.0.1
pip install markdown==3.6
pip install tabulate==0.9.0  # For table formatting
pip install redis==5.0.4  # For conversation cache

# Development
pip install pytest-asyncio
pip install httpx  # For testing SSE
```

**TODO 1.3: Docker Infrastructure**
```yaml
# docker-compose.yml
version: '3.8'
services:
  postgres:
    image: pgvector/pgvector:pg16
    environment:
      POSTGRES_DB: rag_chatbot
      POSTGRES_USER: rag_user
      POSTGRES_PASSWORD: rag_pass
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

**TODO 1.4: Database Schema**
```sql
-- init.sql
CREATE EXTENSION IF NOT EXISTS vector;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Document chunks storage
CREATE TABLE document_chunks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    content TEXT NOT NULL,
    metadata JSONB,
    embedding VECTOR(1536),
    document_id VARCHAR(255),
    chunk_index INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Hybrid search index
CREATE INDEX idx_embedding ON document_chunks 
USING hnsw (embedding vector_cosine_ops)
WITH (m = 16, ef_construction = 64);

CREATE INDEX idx_content_fts ON document_chunks 
USING gin(to_tsvector('english', content));

-- Conversation history
CREATE TABLE conversations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id VARCHAR(255) NOT NULL,
    messages JSONB DEFAULT '[]',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_session_id ON conversations(session_id);
```

### Phase 2: LlamaIndex RAG Implementation (Days 3-4)

**TODO 2.1: Configuration Management**
```python
# app/core/config.py
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    # Database
    database_url: str = "postgresql+asyncpg://rag_user:rag_pass@localhost:5432/rag_chatbot"
    redis_url: str = "redis://localhost:6379"
    
    # OpenAI
    openai_api_key: str
    embedding_model: str = "text-embedding-3-small"
    llm_model: str = "gpt-4-turbo-preview"
    
    # RAG settings
    chunk_size: int = 1024
    chunk_overlap: int = 128
    top_k: int = 5
    hybrid_search_alpha: float = 0.5  # Balance between vector and keyword
    
    # Conversation
    max_conversation_history: int = 10
    conversation_ttl: int = 3600  # 1 hour in Redis
    
    class Config:
        env_file = ".env"

settings = Settings()
```

**TODO 2.2: Vector Store Implementation**
```python
# app/core/vector_store.py
from llama_index.vector_stores.postgres import PGVectorStore
from llama_index.core import VectorStoreIndex
from llama_index.embeddings.openai import OpenAIEmbedding
from sqlalchemy import text
import asyncio

class HybridVectorStore:
    def __init__(self, connection_string: str):
        self.vector_store = PGVectorStore.from_params(
            database=connection_string,
            embed_dim=1536,
            hybrid_search=True,
            text_search_config="english"
        )
        
        self.embed_model = OpenAIEmbedding(
            model=settings.embedding_model,
            api_key=settings.openai_api_key
        )
    
    async def hybrid_search(self, query: str, top_k: int = 5, alpha: float = 0.5):
        """Perform hybrid search combining vector and keyword search"""
        # Get query embedding
        query_embedding = await self.embed_model.aget_query_embedding(query)
        
        # Perform hybrid search
        results = await self.vector_store.ahybrid_search(
            query_embedding=query_embedding,
            query_str=query,
            top_k=top_k,
            alpha=alpha  # 0 = pure keyword, 1 = pure vector
        )
        
        return results
```

**TODO 2.3: RAG Engine with Conversation Context**
```python
# app/core/rag_engine.py
from llama_index.core import ServiceContext
from llama_index.llms.openai import OpenAI
from typing import List, Dict
import json

class RAGEngine:
    def __init__(self, vector_store: HybridVectorStore):
        self.vector_store = vector_store
        self.llm = OpenAI(
            model=settings.llm_model,
            api_key=settings.openai_api_key,
            temperature=0.7
        )
        
    async def query_with_context(
        self, 
        query: str, 
        conversation_history: List[Dict],
        session_id: str
    ) -> Dict:
        # Enhance query with conversation context
        context_query = self._build_contextual_query(query, conversation_history)
        
        # Perform hybrid search
        search_results = await self.vector_store.hybrid_search(
            context_query, 
            top_k=settings.top_k,
            alpha=settings.hybrid_search_alpha
        )
        
        # Format context from search results
        context = self._format_context(search_results)
        
        # Generate response
        response = await self._generate_response(
            query, 
            context, 
            conversation_history
        )
        
        return response
    
    def _format_context(self, search_results) -> str:
        """Format search results with source information"""
        context_parts = []
        sources = []
        
        for i, result in enumerate(search_results):
            context_parts.append(f"[Source {i+1}]: {result.text}")
            sources.append({
                "index": i + 1,
                "document": result.metadata.get("document_id", "Unknown"),
                "section": result.metadata.get("section", ""),
                "score": result.score
            })
        
        return {
            "context": "\n\n".join(context_parts),
            "sources": sources
        }
```

### Phase 3: Message Formatting & Quick Replies (Days 5-6)

**TODO 3.1: Message Models**
```python
# app/api/models.py
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional, Literal
from enum import Enum

class ContentType(str, Enum):
    TEXT = "text"
    QUICK_REPLIES = "quick_replies"
    TABLE = "table"
    LIST = "list"

class QuickReply(BaseModel):
    title: str
    payload: str

class TextContent(BaseModel):
    type: Literal["text"]
    text: str

class QuickRepliesContent(BaseModel):
    type: Literal["quick_replies"]
    replies: List[QuickReply]

class TableContent(BaseModel):
    type: Literal["table"]
    headers: List[str]
    rows: List[List[str]]
    caption: Optional[str] = None

class ListContent(BaseModel):
    type: Literal["list"]
    items: List[Dict[str, Any]]
    title: Optional[str] = None

ContentItem = TextContent | QuickRepliesContent | TableContent | ListContent

class Message(BaseModel):
    role: Literal["user", "assistant"]
    content: List[ContentItem]

class ChatMessage(BaseModel):
    message: Message

class ChatRequest(BaseModel):
    query: str
    session_id: str
    payload: Optional[str] = None  # For quick reply payloads
```

**TODO 3.2: Quick Replies Engine**
```python
# app/core/quick_replies_engine.py
class QuickRepliesEngine:
    def __init__(self):
        self.predefined_topics = {
            "getting_started": [
                QuickReply(title="Installation Guide", payload="install_guide"),
                QuickReply(title="Quick Start Tutorial", payload="quick_start"),
                QuickReply(title="System Requirements", payload="requirements")
            ],
            "troubleshooting": [
                QuickReply(title="Common Errors", payload="common_errors"),
                QuickReply(title="Debug Guide", payload="debug_guide"),
                QuickReply(title="Contact Support", payload="support")
            ]
        }
        
    async def generate_quick_replies(
        self, 
        response_text: str, 
        context: Dict,
        query_type: str
    ) -> List[QuickReply]:
        """Generate mix of predefined and dynamic quick replies"""
        quick_replies = []
        
        # Add predefined replies based on detected topic
        topic = self._detect_topic(response_text, context)
        if topic in self.predefined_topics:
            quick_replies.extend(self.predefined_topics[topic][:2])
        
        # Generate contextual follow-ups using LLM
        dynamic_replies = await self._generate_dynamic_replies(
            response_text, 
            context
        )
        quick_replies.extend(dynamic_replies[:2])
        
        return quick_replies[:4]  # Limit to 4 quick replies
    
    async def _generate_dynamic_replies(
        self, 
        response_text: str, 
        context: Dict
    ) -> List[QuickReply]:
        """Use LLM to generate contextual follow-up questions"""
        prompt = f"""Based on this response: "{response_text[:200]}..."
        Generate 2 natural follow-up questions a user might ask.
        Format: question|payload_key
        Example: "How do I configure this?|configure_guide"
        """
        
        # Call LLM to generate suggestions
        suggestions = await self.llm.agenerate(prompt)
        
        # Parse and return quick replies
        replies = []
        for line in suggestions.split('\n'):
            if '|' in line:
                title, payload = line.split('|', 1)
                replies.append(QuickReply(
                    title=title.strip(),
                    payload=payload.strip()
                ))
        
        return replies
```

**TODO 3.3: Message Formatter**
```python
# app/utils/message_formatter.py
class MessageFormatter:
    @staticmethod
    def format_response(
        text: str,
        sources: List[Dict],
        quick_replies: List[QuickReply],
        tables: Optional[List[Dict]] = None
    ) -> ChatMessage:
        """Format response into the required message structure"""
        content = []
        
        # Add main text response
        content.append(TextContent(type="text", text=text))
        
        # Add sources as a list
        if sources:
            source_items = []
            for source in sources:
                source_items.append({
                    "title": f"Source {source['index']}: {source['document']}",
                    "link": f"#source-{source['index']}",
                    "relevance": f"{source['score']:.2f}"
                })
            
            content.append(ListContent(
                type="list",
                title="Sources",
                items=source_items
            ))
        
        # Add tables if present
        if tables:
            for table in tables:
                content.append(TableContent(
                    type="table",
                    headers=table["headers"],
                    rows=table["rows"],
                    caption=table.get("caption")
                ))
        
        # Add quick replies
        if quick_replies:
            content.append(QuickRepliesContent(
                type="quick_replies",
                replies=quick_replies
            ))
        
        return ChatMessage(
            message=Message(
                role="assistant",
                content=content
            )
        )
```

### Phase 4: FastAPI SSE Implementation (Days 7-8)

**TODO 4.1: SSE Chat Endpoint**
```python
# app/api/chat.py
from fastapi import APIRouter, HTTPException
from sse_starlette.sse import EventSourceResponse
import json
import asyncio
from typing import AsyncGenerator

router = APIRouter()

@router.post("/chat/stream")
async def stream_chat(request: ChatRequest):
    """SSE endpoint for streaming chat responses"""
    async def event_generator() -> AsyncGenerator[str, None]:
        try:
            # Get conversation history
            conversation = await conversation_manager.get_conversation(
                request.session_id
            )
            
            # Process query through RAG engine
            response_data = await rag_engine.query_with_context(
                query=request.query,
                conversation_history=conversation.messages,
                session_id=request.session_id
            )
            
            # Generate quick replies
            quick_replies = await quick_replies_engine.generate_quick_replies(
                response_text=response_data["text"],
                context=response_data["context"],
                query_type=response_data.get("query_type", "general")
            )
            
            # Format message
            message = MessageFormatter.format_response(
                text=response_data["text"],
                sources=response_data["sources"],
                quick_replies=quick_replies,
                tables=response_data.get("tables")
            )
            
            # Stream the response
            yield {
                "event": "message",
                "data": json.dumps(message.dict())
            }
            
            # Update conversation history
            await conversation_manager.add_message(
                session_id=request.session_id,
                role="user",
                content=request.query
            )
            await conversation_manager.add_message(
                session_id=request.session_id,
                role="assistant",
                content=response_data["text"]
            )
            
            # Send completion event
            yield {
                "event": "done",
                "data": json.dumps({"status": "completed"})
            }
            
        except Exception as e:
            yield {
                "event": "error",
                "data": json.dumps({"error": str(e)})
            }
    
    return EventSourceResponse(event_generator())

@router.post("/chat/quick-reply")
async def handle_quick_reply(payload: str, session_id: str):
    """Handle quick reply payload clicks"""
    # Define payload handlers
    payload_handlers = {
        "install_guide": "How do I install the software?",
        "quick_start": "Show me a quick start tutorial",
        "requirements": "What are the system requirements?",
        # Add more mappings
    }
    
    # Convert payload to query
    query = payload_handlers.get(payload, payload)
    
    # Reuse the stream_chat logic
    return await stream_chat(ChatRequest(
        query=query,
        session_id=session_id,
        payload=payload
    ))
```

**TODO 4.2: Angular Integration Example**
```typescript
// Angular service for SSE connection
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ChatService {
  private baseUrl = 'http://localhost:8000';
  
  streamChat(query: string, sessionId: string): Observable<any> {
    return new Observable(observer => {
      const eventSource = new EventSource(
        `${this.baseUrl}/chat/stream`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ query, session_id: sessionId })
        }
      );
      
      eventSource.addEventListener('message', (event) => {
        const data = JSON.parse(event.data);
        observer.next(data);
      });
      
      eventSource.addEventListener('done', () => {
        eventSource.close();
        observer.complete();
      });
      
      eventSource.addEventListener('error', (error) => {
        eventSource.close();
        observer.error(error);
      });
      
      return () => eventSource.close();
    });
  }
  
  handleQuickReply(payload: string, sessionId: string): Observable<any> {
    return this.http.post(`${this.baseUrl}/chat/quick-reply`, {
      payload,
      session_id: sessionId
    });
  }
}
```

**TODO 4.3: Main Application Setup**
```python
# app/main.py
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import logging

# Initialize components
rag_engine = None
conversation_manager = None
quick_replies_engine = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    global rag_engine, conversation_manager, quick_replies_engine
    
    # Initialize vector store
    vector_store = HybridVectorStore(settings.database_url)
    
    # Initialize engines
    rag_engine = RAGEngine(vector_store)
    conversation_manager = ConversationManager(settings.redis_url)
    quick_replies_engine = QuickRepliesEngine()
    
    yield
    
    # Shutdown
    await conversation_manager.close()

app = FastAPI(lifespan=lifespan)

# CORS for Angular
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:4200"],  # Angular dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(chat_router, prefix="/api")

@app.get("/health")
async def health_check():
    return {"status": "healthy"}
```

### Phase 5: Testing & Deployment (Days 9-10)

**TODO 5.1: Testing Suite**
```python
# tests/test_sse_chat.py
import pytest
from httpx import AsyncClient
from httpx_sse import aconnect_sse

@pytest.mark.asyncio
async def test_chat_stream():
    async with AsyncClient(app=app, base_url="http://test") as client:
        async with aconnect_sse(
            client,
            "POST",
            "/api/chat/stream",
            json={
                "query": "How do I install the software?",
                "session_id": "test-session"
            }
        ) as event_source:
            events = []
            async for sse in event_source.aiter_sse():
                events.append(sse)
            
            # Verify message event
            assert events[0].event == "message"
            message_data = json.loads(events[0].data)
            assert message_data["message"]["role"] == "assistant"
            
            # Verify quick replies exist
            quick_replies = next(
                c for c in message_data["message"]["content"] 
                if c["type"] == "quick_replies"
            )
            assert len(quick_replies["replies"]) > 0
```

**TODO 5.2: Document Indexing Script**
```python
# scripts/index_documents.py
async def index_documents():
    """Index markdown and PDF documents"""
    from llama_index.core import SimpleDirectoryReader
    from llama_index.core.node_parser import MarkdownNodeParser
    
    # Load documents
    documents = SimpleDirectoryReader(
        input_dir="./documents",
        file_extractor={
            ".md": MarkdownReader(),
            ".pdf": PDFReader()
        }
    ).load_data()
    
    # Parse into nodes
    parser = MarkdownNodeParser.from_defaults(
        chunk_size=settings.chunk_size,
        chunk_overlap=settings.chunk_overlap
    )
    
    nodes = parser.get_nodes_from_documents(documents)
    
    # Index nodes
    vector_store = HybridVectorStore(settings.database_url)
    await vector_store.add_nodes(nodes)
    
    print(f"Indexed {len(nodes)} document chunks")
```

### Recommended Payload Handling Strategy

For the quick reply payloads, I recommend this flexible approach:

```python
# Payload format: "action:target:context"
# Examples:
# - "query:installation:windows" -> Triggers installation query for Windows
# - "navigate:section:troubleshooting" -> Navigate to troubleshooting section
# - "followup:previous:more_details" -> Follow-up on previous response

class PayloadHandler:
    async def handle(self, payload: str, session_id: str, context: Dict):
        parts = payload.split(":")
        action = parts[0]
        
        if action == "query":
            # Direct query mapping
            return await self._handle_query(parts[1:], context)
        elif action == "navigate":
            # Document navigation
            return await self._handle_navigation(parts[1:])
        elif action == "followup":
            # Context-aware follow-up
            return await self._handle_followup(parts[1:], session_id)
```

This gives you flexibility to evolve the payload system as you discover user patterns.

Would you like me to elaborate on any specific part of this plan or create more detailed implementation examples for particular components?