#!/usr/bin/env python3
"""
Document indexing script for the RAG chatbot.
This script processes documents from the documents/ directory and indexes them into the vector store
using a combination of MarkdownNodeParser and SemanticDoubleMergingSplitterNodeParser.
"""

import asyncio
import sys
import logging
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any
import uuid

from sqlalchemy import make_url

# Add the parent directory to the path so we can import our modules
sys.path.append(str(Path(__file__).parent.parent))

from llama_index.core import SimpleDirectoryReader, Document
from llama_index.core.node_parser import (
    MarkdownNodeParser,
    SemanticDoubleMergingSplitterNodeParser,
    LanguageConfig,
)
from llama_index.core.schema import BaseNode
from llama_index.core.ingestion import IngestionPipeline
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.vector_stores.postgres import PGVectorStore
from llama_index.core import VectorStoreIndex
from llama_index.core import Simple<PERSON>ire<PERSON>y<PERSON>ead<PERSON>, StorageContext
import psycopg2
from psycopg2.extras import RealDictCursor
import json
from llama_index.core import Settings as LlamaIndexSettings

from app.core.config import settings

LlamaIndexSettings.embed_model = OpenAIEmbedding(
    model="text-embedding-3-small", api_key=settings.openai_api_key
)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DocumentChunkStore:
    """Handles storing document chunks in PostgreSQL"""

    def __init__(self, database_url: str):
        # Convert SQLAlchemy URL format to psycopg2 format
        self.database_url = database_url
        self.vectore_store_index = self.create_vector_store_index()

    def create_vector_store_index(self):
        url = make_url(settings.database_url)
        vector_store = PGVectorStore.from_params(
            database=url.database,
            host=url.host,
            password=url.password,
            port=url.port,
            user=url.username,
            table_name="document_chunks",
            embed_dim=1536,  # openai embedding dimension
            hnsw_kwargs={
                "hnsw_m": 32,
                "hnsw_ef_construction": 200,
                "hnsw_ef_search": 100,
                "hnsw_dist_method": "vector_cosine_ops",
            },
        )

        storage_context = StorageContext.from_defaults(vector_store=vector_store)
        # index = VectorStoreIndex.from_vector_store(vector_store=vector_store)
        index = VectorStoreIndex.from_documents(
            [], storage_context=storage_context, show_progress=True
        )
        return index

    def store_chunks(self, nodes: List[BaseNode]):
        """Store document chunks with embeddings in PostgreSQL"""
        self.vectore_store_index.insert_nodes(nodes)


def create_node_parsers():
    """Create and configure the node parsers"""

    # Configure semantic splitter with spaCy
    language_config = LanguageConfig(language="english", spacy_model="en_core_web_md")

    semantic_splitter = SemanticDoubleMergingSplitterNodeParser(
        language_config=language_config,
        initial_threshold=0.4,  # Controls initial semantic boundary detection
        appending_threshold=0.5,  # Threshold for appending text to existing chunks
        merging_threshold=0.5,  # Threshold for merging adjacent chunks
        max_chunk_size=settings.chunk_size,  # Use configured chunk size
    )

    # Configure markdown parser
    markdown_parser = MarkdownNodeParser(
        include_metadata=True,
        include_prev_next_rel=True,
    )

    logger.info("✅ Node parsers configured successfully")
    return markdown_parser, semantic_splitter


def process_documents_with_pipeline(documents: List[Document]) -> List[BaseNode]:
    """Process documents using ingestion pipeline with both parsers"""

    markdown_parser, semantic_splitter = create_node_parsers()

    # Create ingestion pipeline with both parsers
    pipeline = IngestionPipeline(
        transformations=[
            markdown_parser,
            semantic_splitter,
        ],
        vector_store=None,  # We'll handle embeddings separately
    )

    logger.info("🔄 Processing documents through ingestion pipeline...")
    nodes = pipeline.run(documents=documents)

    logger.info(
        f"✅ Pipeline processed {len(documents)} documents into {len(nodes)} nodes"
    )
    return nodes


def generate_embeddings(nodes: List[BaseNode]) -> List[List[float]]:
    """Generate embeddings for the document chunks"""
    if not settings.openai_api_key:
        logger.warning("⚠️ No OpenAI API key provided. Skipping embedding generation.")
        return [[0.0] * 1536 for _ in nodes]  # Return dummy embeddings

    logger.info("🔄 Generating embeddings using OpenAI...")

    embed_model = OpenAIEmbedding(
        model=settings.embedding_model, api_key=settings.openai_api_key
    )

    # Extract text content from nodes
    texts = [node.get_content() for node in nodes]

    # Generate embeddings in batches to avoid rate limits
    batch_size = 100
    all_embeddings = []

    for i in range(0, len(texts), batch_size):
        batch_texts = texts[i : i + batch_size]
        batch_embeddings = embed_model.get_text_embedding_batch(batch_texts)
        all_embeddings.extend(batch_embeddings)

        logger.info(
            f"📊 Generated embeddings for batch {i//batch_size + 1}/{(len(texts) + batch_size - 1)//batch_size}"
        )

    logger.info(f"✅ Generated {len(all_embeddings)} embeddings")
    return all_embeddings


async def index_documents():
    """Index markdown and PDF documents using semantic and markdown parsing"""
    logger.info("🚀 Starting document indexing with advanced text processing...")

    try:
        # Load documents from the documents directory
        documents_dir = Path(__file__).parent.parent / "documents"

        if not documents_dir.exists():
            logger.info(
                f"📁 Documents directory {documents_dir} does not exist. Creating it..."
            )
            documents_dir.mkdir(parents=True, exist_ok=True)
            logger.info(
                "📝 Please add your documents to the documents/ directory and run this script again."
            )
            return

        # Check if there are any documents
        document_files = list(documents_dir.glob("**/*"))
        document_files = [
            f
            for f in document_files
            if f.is_file() and f.suffix in [".md", ".txt", ".pdf", ".rst"]
        ]

        if not document_files:
            logger.warning("📂 No documents found in the documents/ directory.")
            logger.info("📋 Supported formats: .md, .txt, .pdf, .rst")
            return

        logger.info(f"📚 Found {len(document_files)} documents to index:")
        for doc in document_files:
            logger.info(f"  📄 {doc.name}")

        # Load documents
        logger.info("📖 Loading documents...")
        documents = SimpleDirectoryReader(
            input_dir=str(documents_dir),
            recursive=True,
            filename_as_id=True,  # Use filename as document ID
        ).load_data()

        logger.info(f"✅ Loaded {len(documents)} documents")

        # Add metadata to documents
        timestamp = datetime.now().isoformat()
        for i, doc in enumerate(documents):
            doc.metadata.update(
                {
                    "source": "document_indexing",
                    "indexed_at": timestamp,
                    "document_index": i,
                    "total_documents": len(documents),
                }
            )

        # Process documents through pipeline
        nodes = process_documents_with_pipeline(documents)

        # Add chunk metadata
        for i, node in enumerate(nodes):
            node.metadata.update(
                {
                    "chunk_index": i,
                    "total_chunks": len(nodes),
                    "processing_method": "markdown_semantic_pipeline",
                }
            )

        # Generate embeddings (this will create dummy embeddings if no OpenAI key)
        # embeddings = generate_embeddings(nodes)

        # Try to store in PostgreSQL if database is available
        try:
            chunk_store = DocumentChunkStore(settings.database_url)
            # chunk_store.create_table_if_not_exists()
            chunk_store.store_chunks(nodes)
            logger.info("✅ Successfully stored chunks in PostgreSQL database")
        except Exception as db_error:
            logger.warning(f'⚠️ Database storage failed: "{db_error}"')
            import traceback

            traceback.print_exc()
            # logger.info("💾 Saving processed chunks to local JSON file instead...")

            # # Save to local file as fallback
            # output_file = Path(__file__).parent.parent / "processed_chunks.json"
            # chunks_data = []
            # for node, embedding in zip(nodes, embeddings):
            #     chunks_data.append(
            #         {
            #             "content": node.text,
            #             "metadata": node.metadata,
            #             "embedding_sample": (
            #                 embedding[:5] if embedding else []
            #             ),  # Only first 5 dimensions for file size
            #             "embedding_size": len(embedding) if embedding else 0,
            #         }
            #     )

            # with open(output_file, "w", encoding="utf-8") as f:
            #     json.dump(chunks_data, f, indent=2, ensure_ascii=False, default=str)

            # logger.info(f"💾 Saved {len(chunks_data)} chunks to {output_file}")

        # # Summary statistics
        # total_characters = sum(len(node.text) for node in nodes)
        # avg_chunk_size = total_characters / len(nodes) if nodes else 0

        # logger.info("📊 Indexing Summary:")
        # logger.info(f"  📚 Documents processed: {len(documents)}")
        # logger.info(f"  🧩 Total chunks created: {len(nodes)}")
        # logger.info(f"  📏 Average chunk size: {avg_chunk_size:.0f} characters")
        # logger.info(f"  🗄️ Total characters indexed: {total_characters:,}")
        # logger.info(f"  🕒 Indexed at: {timestamp}")

        # # Show sample chunks
        # logger.info("\n📋 Sample processed chunks:")
        # for i, node in enumerate(nodes[:3]):
        #     logger.info(f"  Chunk {i+1}:")
        #     logger.info(f"    Content preview: {node.text[:100]}...")
        #     logger.info(f"    Metadata: {node.metadata}")
        #     logger.info(f"    Character count: {len(node.text)}")
        #     logger.info("")

        # logger.info("🎉 Document indexing completed successfully!")

    except Exception as e:
        logger.error(f"❌ Error during document indexing: {e}")
        import traceback

        traceback.print_exc()
        raise


def main():
    """Main entry point"""
    print("🤖 RAG Chatbot Document Indexer")
    print("=" * 50)
    print("📋 Features:")
    print("  🔍 SemanticDoubleMergingSplitterNodeParser for intelligent text splitting")
    print("  📝 MarkdownNodeParser for structure-aware parsing")
    print("  🗄️ PostgreSQL storage with pgvector for efficient similarity search")
    print("  🧠 OpenAI embeddings for semantic understanding")
    print("=" * 50)

    # Run the indexing function
    asyncio.run(index_documents())


if __name__ == "__main__":
    main()
