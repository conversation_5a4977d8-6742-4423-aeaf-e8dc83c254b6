import pytest
import asyncio
from httpx import Async<PERSON>lient
from fastapi.testclient import Test<PERSON><PERSON>
import json

from app.main import app


@pytest.fixture
def client():
    return TestClient(app)


@pytest.fixture
async def async_client():
    async with Async<PERSON><PERSON>(app=app, base_url="http://test") as client:
        yield client


class TestChatAPI:
    """Test suite for chat API endpoints"""
    
    def test_health_endpoint(self, client):
        """Test the health check endpoint"""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "service" in data
    
    def test_root_endpoint(self, client):
        """Test the root endpoint"""
        response = client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "version" in data
        assert "status" in data
    
    def test_chat_health_endpoint(self, client):
        """Test the chat-specific health endpoint"""
        response = client.get("/api/chat/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert data["service"] == "chat-api"
    
    @pytest.mark.asyncio
    async def test_session_info_endpoint(self, async_client):
        """Test getting session information"""
        session_id = "test-session-123"
        response = await async_client.get(f"/api/chat/session/{session_id}/info")
        assert response.status_code == 200
        data = response.json()
        assert "session_id" in data
        assert "message_count" in data
    
    @pytest.mark.asyncio
    async def test_clear_session_endpoint(self, async_client):
        """Test clearing a session"""
        session_id = "test-session-456"
        response = await async_client.delete(f"/api/chat/session/{session_id}")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "session_cleared"
        assert data["session_id"] == session_id


class TestChatModels:
    """Test suite for chat models"""
    
    def test_chat_request_model(self):
        """Test ChatRequest model validation"""
        from app.api.models import ChatRequest
        
        # Valid request
        request = ChatRequest(
            query="Hello, how are you?",
            session_id="test-session",
            payload=None
        )
        assert request.query == "Hello, how are you?"
        assert request.session_id == "test-session"
        assert request.payload is None
    
    def test_quick_reply_model(self):
        """Test QuickReply model validation"""
        from app.api.models import QuickReply
        
        reply = QuickReply(
            title="Tell me more",
            payload="more_info"
        )
        assert reply.title == "Tell me more"
        assert reply.payload == "more_info"
    
    def test_text_content_model(self):
        """Test TextContent model validation"""
        from app.api.models import TextContent
        
        content = TextContent(
            type="text",
            text="This is a sample response"
        )
        assert content.type == "text"
        assert content.text == "This is a sample response"


class TestMessageFormatter:
    """Test suite for message formatter"""
    
    def test_format_response_basic(self):
        """Test basic response formatting"""
        from app.utils.message_formatter import MessageFormatter
        from app.api.models import QuickReply
        
        text = "This is a test response"
        sources = [
            {"index": 1, "document": "test_doc.md", "score": 0.95}
        ]
        quick_replies = [
            QuickReply(title="More info", payload="more_info")
        ]
        
        formatted = MessageFormatter.format_response(
            text=text,
            sources=sources,
            quick_replies=quick_replies
        )
        
        assert formatted.message.role == "assistant"
        assert len(formatted.message.content) >= 2  # At least text and quick replies
        
        # Check text content
        text_content = next(
            (c for c in formatted.message.content if c.type == "text"), 
            None
        )
        assert text_content is not None
        assert text_content.text == text
        
        # Check quick replies
        quick_replies_content = next(
            (c for c in formatted.message.content if c.type == "quick_replies"), 
            None
        )
        assert quick_replies_content is not None
        assert len(quick_replies_content.replies) == 1
        assert quick_replies_content.replies[0].title == "More info"


class TestQuickRepliesEngine:
    """Test suite for quick replies engine"""
    
    def test_topic_detection(self):
        """Test topic detection logic"""
        from app.core.quick_replies_engine import QuickRepliesEngine
        
        engine = QuickRepliesEngine()
        
        # Test getting started topic
        assert engine._detect_topic("How to install the software", {}) == "getting_started"
        assert engine._detect_topic("Getting started guide", {}) == "getting_started"
        
        # Test troubleshooting topic
        assert engine._detect_topic("I'm having an error", {}) == "troubleshooting"
        assert engine._detect_topic("Debug this issue", {}) == "troubleshooting"
        
        # Test general topic
        assert engine._detect_topic("Tell me about features", {}) == "general"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])