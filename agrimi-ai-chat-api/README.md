# Agrimi AI Chat API

A production-ready AI chatbot API built with FastAPI, LlamaIndex, and PostgreSQL for Retrieval-Augmented Generation (RAG) capabilities.

## Features

- 🚀 **FastAPI** with Server-Sent Events (SSE) for real-time streaming
- 🧠 **LlamaIndex** for advanced RAG capabilities  
- 🗄️ **PostgreSQL with pgvector** for hybrid vector search
- 💾 **Redis** for conversation session management
- 🔧 **Pydantic** for configuration and data validation
- ✨ **Quick Replies** with dynamic and predefined responses
- 📊 **Structured Responses** with tables, lists, and sources
- 🧪 **Comprehensive Testing** suite included

## Project Structure

```
agrimi-ai-chat-api/
├── app/
│   ├── api/
│   │   ├── __init__.py
│   │   ├── chat.py          # Chat endpoints with SSE
│   │   └── models.py        # Pydantic models
│   ├── core/
│   │   ├── __init__.py
│   │   ├── config.py        # Configuration management
│   │   ├── rag_engine.py    # RAG implementation
│   │   ├── vector_store.py  # Hybrid vector store
│   │   ├── conversation_manager.py  # Session handling
│   │   └── quick_replies_engine.py  # Quick replies logic
│   ├── utils/
│   │   ├── __init__.py
│   │   └── message_formatter.py     # Response formatting
│   ├── db/
│   │   └── __init__.py
│   └── main.py              # FastAPI application
├── documents/               # Place your documents here
├── scripts/
│   └── index_documents.py   # Document indexing script
├── tests/
│   ├── __init__.py
│   └── test_chat_api.py     # Test suite
├── docker-compose.yml       # PostgreSQL + Redis setup
├── requirements.txt         # Python dependencies
├── init.sql                 # Database initialization
├── .env.example            # Environment variables template
└── README.md               # This file
```

## Quick Start

### 1. Clone and Setup

```bash
cd agrimi-ai-chat-api
cp .env.example .env
# Edit .env with your OpenAI API key
```

### 2. Install Dependencies

```bash
pip install -r requirements.txt
```

### 3. Start Services

```bash
# Start PostgreSQL and Redis
docker-compose up -d

# Verify services are running
docker-compose ps
```

### 4. Configure Environment

Edit `.env` file:
```bash
OPENAI_API_KEY=your_openai_api_key_here
DATABASE_URL=postgresql://rag_user:rag_pass@localhost:5432/rag_chatbot
REDIS_URL=redis://localhost:6379
```

### 5. Index Documents (Optional)

```bash
# Add your documents to the documents/ directory
mkdir -p documents
echo "# Sample Document\nThis is a sample document for testing." > documents/sample.md

# Run the indexing script
python scripts/index_documents.py
```

### 6. Start the API

```bash
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

The API will be available at `http://localhost:8000`

## API Endpoints

### Chat Endpoints

- **POST** `/api/chat/stream` - Stream chat responses via SSE
- **POST** `/api/chat/quick-reply` - Handle quick reply actions
- **GET** `/api/chat/health` - Chat service health check
- **DELETE** `/api/chat/session/{session_id}` - Clear session history
- **GET** `/api/chat/session/{session_id}/info` - Get session information

### General Endpoints

- **GET** `/` - API information
- **GET** `/health` - Overall health check

### Example Usage

#### Streaming Chat Request

```bash
curl -X POST "http://localhost:8000/api/chat/stream" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "What is the main topic of the documentation?",
    "session_id": "user-123"
  }'
```

#### Quick Reply

```bash
curl -X POST "http://localhost:8000/api/chat/quick-reply" \
  -H "Content-Type: application/json" \
  -d '{
    "payload": "more_info",
    "session_id": "user-123"
  }'
```

## Response Format

The API returns structured responses with multiple content types:

```json
{
  "message": {
    "role": "assistant",
    "content": [
      {
        "type": "text",
        "text": "Here's the information you requested..."
      },
      {
        "type": "list",
        "title": "Sources",
        "items": [
          {
            "title": "Source 1: document.md",
            "link": "#source-1",
            "relevance": "0.95"
          }
        ]
      },
      {
        "type": "quick_replies",
        "replies": [
          {
            "title": "Tell me more",
            "payload": "more_info"
          },
          {
            "title": "Show examples",
            "payload": "show_examples"
          }
        ]
      }
    ]
  }
}
```

## Testing

Run the test suite:

```bash
# Install test dependencies
pip install pytest pytest-asyncio

# Run tests
pytest tests/ -v
```

## Configuration

All configuration is managed via environment variables. See `.env.example` for available options:

- **Database**: PostgreSQL connection settings
- **Redis**: Redis connection for session management  
- **OpenAI**: API key and model configurations
- **RAG**: Chunk size, overlap, and search parameters
- **Conversation**: History limits and TTL settings

## Development

### Adding New Documents

1. Place documents in the `documents/` directory
2. Run the indexing script: `python scripts/index_documents.py`
3. Supported formats: `.md`, `.txt`, `.pdf`

### Extending Quick Replies

Edit `app/core/quick_replies_engine.py` to add new predefined topics or modify the dynamic generation logic.

### Custom Response Types

Add new content types in `app/api/models.py` and update the message formatter in `app/utils/message_formatter.py`.

## Production Deployment

### Environment Variables

Set these in production:
```bash
DATABASE_URL=your_production_postgres_url
REDIS_URL=your_production_redis_url
OPENAI_API_KEY=your_openai_api_key
```

### Docker Deployment

```bash
# Build and run with Docker
docker build -t agrimi-ai-chat-api .
docker run -p 8000:8000 --env-file .env agrimi-ai-chat-api
```

### Scaling Considerations

- Use a managed PostgreSQL service with pgvector support
- Configure Redis clustering for high availability
- Set up proper logging and monitoring
- Consider rate limiting for the OpenAI API

## Architecture

The system follows a modular architecture:

1. **FastAPI Application** (`app/main.py`) - Main application with CORS and middleware
2. **API Layer** (`app/api/`) - Request/response handling and SSE streaming
3. **Core Services** (`app/core/`) - Business logic for RAG, conversation, and quick replies
4. **Data Models** (`app/api/models.py`) - Pydantic models for validation
5. **Utilities** (`app/utils/`) - Helper functions for formatting and processing

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For issues and questions:
1. Check the existing issues in the repository
2. Create a new issue with detailed information
3. Include logs and error messages when applicable

---

Built with ❤️ using FastAPI, LlamaIndex, and PostgreSQL