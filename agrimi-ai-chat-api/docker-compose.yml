version: "3.8"
services:
    # postgres:
    #   image: pgvector/pgvector:pg16
    #   environment:
    #     POSTGRES_DB: rag_chatbot
    #     POSTGRES_USER: rag_user
    #     POSTGRES_PASSWORD: rag_pass
    #   ports:
    #     - "5432:5432"
    #   volumes:
    #     - postgres_data:/var/lib/postgresql/data
    #     - ./init.sql:/docker-entrypoint-initdb.d/init.sql

    redis:
        image: redis:7-alpine
        ports:
            - "6380:6379"
        volumes:
            - redis_data:/data

volumes:
    postgres_data:
    redis_data:
