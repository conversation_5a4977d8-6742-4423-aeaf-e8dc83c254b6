# Core dependencies
llama-index==0.13.0
llama-index-core
llama-index-vector-stores-postgres
llama-index-embeddings-openai
llama-index-llms-openai
llama-index-llms-gemini
llama-index-instrumentation

# FastAPI + SSE
fastapi==0.116.1
uvicorn[standard]==0.35.0
starlette==0.47
anyio>=4.7.0
sse-starlette==3.0.2

# Database
pgvector==0.4.1
asyncpg==0.30.0
sqlalchemy==2.0.42
alembic==1.16.4

# Utils
pydantic>=2.11.5
pydantic-core
pydantic_core
pydantic-settings
python-dotenv
pypdf2
markdown
tabulate
redis

# Development
pytest
pytest-asyncio
httpx
click
h11
idna
tiktoken
regex
wrapt
deprecated
typing_extensions
typing_inspection

# LLMs
openai>=1.81.0,<2
google-generativeai>=0.5.2